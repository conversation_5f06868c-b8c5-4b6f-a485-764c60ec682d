#!/usr/bin/env python3
"""
UDP Test Script for Map Application

This script sends test target data to the map application
to verify UDP communication and target display functionality.
"""

import socket
import json
import time
import random
import sys

def send_udp_message(message, host='localhost', port=12345):
    """Send a UDP message to the map application."""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        message_bytes = json.dumps(message).encode('utf-8')
        sock.sendto(message_bytes, (host, port))
        sock.close()
        print(f"Sent: {json.dumps(message, indent=2)}")
        return True
    except Exception as e:
        print(f"Error sending message: {e}")
        return False

def create_target_message(target_id, target_type, lat, lon, action="add", **kwargs):
    """Create a target message."""
    message = {
        "type": "target",
        "action": action,
        "id": target_id,
        "targettype": target_type,
        "latitude": lat,
        "longitude": lon,
        "altitude": kwargs.get("altitude", 0.0),
        "name": kwargs.get("name", f"Target {target_id}"),
        "description": kwargs.get("description", f"Test target of type {target_type}")
    }
    return message

def test_basic_targets():
    """Test basic target creation."""
    print("=== Testing Basic Target Creation ===")
    
    # Beijing area coordinates
    base_lat = 39.9093
    base_lon = 116.3974
    
    targets = [
        ("target_001", "enemy", base_lat + 0.01, base_lon + 0.01),
        ("target_002", "friendly", base_lat - 0.01, base_lon + 0.01),
        ("target_003", "neutral", base_lat + 0.01, base_lon - 0.01),
        ("target_004", "hostile", base_lat - 0.01, base_lon - 0.01),
    ]
    
    for target_id, target_type, lat, lon in targets:
        message = create_target_message(target_id, target_type, lat, lon)
        send_udp_message(message)
        time.sleep(0.5)

def test_target_updates():
    """Test target position updates."""
    print("\n=== Testing Target Updates ===")
    
    # Update target_001 position
    for i in range(5):
        lat = 39.9093 + (i * 0.001)
        lon = 116.3974 + (i * 0.001)
        
        message = create_target_message(
            "target_001", "enemy", lat, lon, 
            action="update",
            name="Moving Enemy Target"
        )
        send_udp_message(message)
        time.sleep(1)

def test_target_removal():
    """Test target removal."""
    print("\n=== Testing Target Removal ===")
    
    # Remove some targets
    for target_id in ["target_002", "target_004"]:
        message = {
            "type": "target",
            "action": "remove",
            "id": target_id
        }
        send_udp_message(message)
        time.sleep(0.5)

def test_random_targets():
    """Test with random target data."""
    print("\n=== Testing Random Targets ===")
    
    target_types = ["enemy", "friendly", "neutral", "hostile", "safe", "threat"]
    
    for i in range(10):
        target_id = f"random_{i:03d}"
        target_type = random.choice(target_types)
        
        # Random position around Beijing
        lat = 39.9093 + random.uniform(-0.05, 0.05)
        lon = 116.3974 + random.uniform(-0.05, 0.05)
        alt = random.uniform(0, 1000)
        
        message = create_target_message(
            target_id, target_type, lat, lon,
            altitude=alt,
            name=f"Random {target_type.title()} {i}",
            description=f"Randomly generated {target_type} target for testing"
        )
        send_udp_message(message)
        time.sleep(0.2)

def test_control_messages():
    """Test control messages."""
    print("\n=== Testing Control Messages ===")
    
    # Ping message
    ping_message = {
        "type": "control",
        "command": "ping"
    }
    send_udp_message(ping_message)
    time.sleep(0.5)
    
    # Status request
    status_message = {
        "type": "control",
        "command": "status"
    }
    send_udp_message(status_message)

def interactive_mode():
    """Interactive mode for manual testing."""
    print("\n=== Interactive Mode ===")
    print("Commands:")
    print("  add <id> <type> <lat> <lon> - Add target")
    print("  update <id> <lat> <lon> - Update target position")
    print("  remove <id> - Remove target")
    print("  ping - Send ping")
    print("  status - Request status")
    print("  quit - Exit")
    
    while True:
        try:
            cmd = input("\n> ").strip().split()
            if not cmd:
                continue
                
            if cmd[0] == "quit":
                break
            elif cmd[0] == "add" and len(cmd) >= 5:
                target_id, target_type, lat, lon = cmd[1], cmd[2], float(cmd[3]), float(cmd[4])
                message = create_target_message(target_id, target_type, lat, lon)
                send_udp_message(message)
            elif cmd[0] == "update" and len(cmd) >= 4:
                target_id, lat, lon = cmd[1], float(cmd[2]), float(cmd[3])
                message = create_target_message(target_id, "unknown", lat, lon, action="update")
                send_udp_message(message)
            elif cmd[0] == "remove" and len(cmd) >= 2:
                message = {"type": "target", "action": "remove", "id": cmd[1]}
                send_udp_message(message)
            elif cmd[0] == "ping":
                send_udp_message({"type": "control", "command": "ping"})
            elif cmd[0] == "status":
                send_udp_message({"type": "control", "command": "status"})
            else:
                print("Invalid command or arguments")
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"Error: {e}")

def main():
    """Main test function."""
    print("UDP Test Script for Map Application")
    print("===================================")
    
    if len(sys.argv) > 1:
        mode = sys.argv[1]
    else:
        mode = "all"
    
    if mode == "basic":
        test_basic_targets()
    elif mode == "updates":
        test_target_updates()
    elif mode == "removal":
        test_target_removal()
    elif mode == "random":
        test_random_targets()
    elif mode == "control":
        test_control_messages()
    elif mode == "interactive":
        interactive_mode()
    elif mode == "all":
        test_basic_targets()
        time.sleep(2)
        test_target_updates()
        time.sleep(2)
        test_target_removal()
        time.sleep(2)
        test_control_messages()
        time.sleep(2)
        test_random_targets()
    else:
        print(f"Unknown mode: {mode}")
        print("Available modes: basic, updates, removal, random, control, interactive, all")

if __name__ == "__main__":
    main()
