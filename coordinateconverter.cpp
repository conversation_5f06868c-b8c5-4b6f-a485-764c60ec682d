#include "coordinateconverter.h"
#include <algorithm>

QPointF CoordinateConverter::latLonToWebMercator(double lat, double lon)
{
    // Clamp coordinates to valid bounds
    lat = clampLatitude(lat);
    lon = clampLongitude(lon);
    
    // Convert to radians
    double latRad = lat * M_PI / 180.0;
    double lonRad = lon * M_PI / 180.0;
    
    // Web Mercator projection
    double x = EARTH_RADIUS * lonRad;
    double y = EARTH_RADIUS * std::log(std::tan(M_PI / 4.0 + latRad / 2.0));
    
    return QPointF(x, y);
}

QPointF CoordinateConverter::webMercatorToLatLon(double x, double y)
{
    // Convert from Web Mercator to lat/lon
    double lon = (x / EARTH_RADIUS) * 180.0 / M_PI;
    double lat = (2.0 * std::atan(std::exp(y / EARTH_RADIUS)) - M_PI / 2.0) * 180.0 / M_PI;
    
    // Clamp to valid bounds
    lat = clampLatitude(lat);
    lon = clampLongitude(lon);
    
    return QPointF(lon, lat);
}

QPointF CoordinateConverter::webMercatorToTile(double x, double y, int zoom)
{
    // Calculate tile size at this zoom level
    double tileSize = 2.0 * M_PI * EARTH_RADIUS / std::pow(2.0, zoom);
    
    // Convert to tile coordinates
    double tileX = (x + M_PI * EARTH_RADIUS) / tileSize;
    double tileY = (M_PI * EARTH_RADIUS - y) / tileSize;
    
    return QPointF(tileX, tileY);
}

QPointF CoordinateConverter::tileToWebMercator(double tileX, double tileY, int zoom)
{
    // Calculate tile size at this zoom level
    double tileSize = 2.0 * M_PI * EARTH_RADIUS / std::pow(2.0, zoom);
    
    // Convert to Web Mercator coordinates
    double x = tileX * tileSize - M_PI * EARTH_RADIUS;
    double y = M_PI * EARTH_RADIUS - tileY * tileSize;
    
    return QPointF(x, y);
}

QPointF CoordinateConverter::latLonToTile(double lat, double lon, int zoom)
{
    QPointF mercator = latLonToWebMercator(lat, lon);
    return webMercatorToTile(mercator.x(), mercator.y(), zoom);
}

QPointF CoordinateConverter::tileToLatLon(double tileX, double tileY, int zoom)
{
    QPointF mercator = tileToWebMercator(tileX, tileY, zoom);
    return webMercatorToLatLon(mercator.x(), mercator.y());
}

double CoordinateConverter::getResolution(int zoom)
{
    // Resolution = (2 * π * R) / (256 * 2^zoom)
    // Where R is Earth radius, 256 is tile size in pixels
    return (2.0 * M_PI * EARTH_RADIUS) / (256.0 * std::pow(2.0, zoom));
}

double CoordinateConverter::clampLatitude(double lat)
{
    return std::max(MIN_LATITUDE, std::min(MAX_LATITUDE, lat));
}

double CoordinateConverter::clampLongitude(double lon)
{
    // Wrap longitude to [-180, 180] range
    while (lon > MAX_LONGITUDE) lon -= 360.0;
    while (lon < MIN_LONGITUDE) lon += 360.0;
    return lon;
}
