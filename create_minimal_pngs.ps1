# Create minimal PNG files for the map application
# This creates 1x1 pixel PNG files that are valid but minimal

# Base64 encoded 1x1 transparent PNG
$pngData = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI/hzyku"

# Convert base64 to bytes
$bytes = [System.Convert]::FromBase64String($pngData)

# Create icons directory if it doesn't exist
if (!(Test-Path "icons")) {
    New-Item -ItemType Directory -Path "icons"
}

# Create PNG files
[System.IO.File]::WriteAllBytes("icons/red.png", $bytes)
[System.IO.File]::WriteAllBytes("icons/blue.png", $bytes)
[System.IO.File]::WriteAllBytes("icons/track.png", $bytes)
[System.IO.File]::WriteAllBytes("icons/send.png", $bytes)

Write-Host "Created minimal PNG files:"
Write-Host "- icons/red.png"
Write-Host "- icons/blue.png"
Write-Host "- icons/track.png"
Write-Host "- icons/send.png"
Write-Host ""
Write-Host "Note: These are minimal 1x1 pixel PNG files."
Write-Host "Replace with proper icons for better visual appearance."
