#include "tilemanager.h"
#include <QNetworkRequest>
#include <QNetworkReply>
#include <QMutexLocker>
#include <QDebug>
#include <QBuffer>
#include <QImageReader>
#include <algorithm>

TileManager::TileManager(QObject *parent)
    : QObject(parent)
    , m_networkManager(new QNetworkAccessManager(this))
    , m_baseUrl("http://localhost:8080")
    , m_maxConcurrentRequests(DEFAULT_MAX_CONCURRENT_REQUESTS)
    , m_activeRequests(0)
    , m_queueTimer(new QTimer(this))
    , m_cache(new TileCache(this))
{
    // Setup queue processing timer
    m_queueTimer->setInterval(QUEUE_PROCESS_INTERVAL);
    m_queueTimer->setSingleShot(false);
    connect(m_queueTimer, &QTimer::timeout, this, &TileManager::processRequestQueue);
    m_queueTimer->start();
    
    // Configure network manager
    // Note: setTransferTimeout is available in Qt 5.15+, skip for Qt 5.14
    // m_networkManager->setTransferTimeout(10000); // 10 second timeout
}

TileManager::~TileManager()
{
    // Cancel all active downloads
    QMutexLocker locker(&m_activeDownloadsMutex);
    for (auto reply : m_activeDownloads.keys()) {
        reply->abort();
        reply->deleteLater();
    }
    m_activeDownloads.clear();
}

void TileManager::requestTile(int zoom, int x, int y, int priority)
{
    TileCache::TileKey key{zoom, x, y};
    
    // Check if already cached
    if (m_cache->hasTile(key)) {
        QPixmap pixmap = m_cache->getTile(key);
        if (!pixmap.isNull()) {
            emit tileLoaded(zoom, x, y, pixmap);
            return;
        }
    }
    
    // Check if already in progress
    if (isRequestInProgress(key)) {
        return;
    }
    
    // Add to request queue
    TileRequest request;
    request.key = key;
    request.priority = priority;
    request.requestTime = QDateTime::currentDateTime();
    
    QMutexLocker locker(&m_queueMutex);
    m_requestQueue.enqueue(request);
}

void TileManager::requestTilesForViewport(const QRect& viewport, int zoom)
{
    // Calculate priority based on distance from center
    QPoint center = viewport.center();
    
    for (int x = viewport.left(); x <= viewport.right(); ++x) {
        for (int y = viewport.top(); y <= viewport.bottom(); ++y) {
            // Calculate priority (closer to center = higher priority)
            int dx = abs(x - center.x());
            int dy = abs(y - center.y());
            int distance = dx + dy;
            int priority = 1000 - distance; // Higher number = higher priority
            
            requestTile(zoom, x, y, priority);
        }
    }
}

QPixmap TileManager::getTile(int zoom, int x, int y)
{
    TileCache::TileKey key{zoom, x, y};
    return m_cache->getTile(key);
}

bool TileManager::hasTile(int zoom, int x, int y)
{
    TileCache::TileKey key{zoom, x, y};
    return m_cache->hasTile(key);
}

void TileManager::setBaseUrl(const QString& baseUrl)
{
    m_baseUrl = baseUrl;
}

void TileManager::setMaxConcurrentRequests(int maxRequests)
{
    m_maxConcurrentRequests = qMax(1, maxRequests);
}

void TileManager::clearCache()
{
    m_cache->clear();
}

QPair<qint64, qint64> TileManager::getCacheSize() const
{
    return qMakePair(m_cache->getMemoryCacheSize(), m_cache->getDiskCacheSize());
}

void TileManager::onTileDownloaded()
{
    QNetworkReply* reply = qobject_cast<QNetworkReply*>(sender());
    if (!reply) {
        return;
    }
    
    TileCache::TileKey key;
    {
        QMutexLocker locker(&m_activeDownloadsMutex);
        auto it = m_activeDownloads.find(reply);
        if (it == m_activeDownloads.end()) {
            reply->deleteLater();
            return;
        }
        key = it.value();
        m_activeDownloads.erase(it);
        m_activeRequests--;
    }
    
    if (reply->error() == QNetworkReply::NoError) {
        // Load pixmap from response data
        QByteArray data = reply->readAll();
        QBuffer buffer(&data);
        buffer.open(QIODevice::ReadOnly);
        
        QImageReader reader(&buffer);
        QImage image = reader.read();
        
        if (!image.isNull()) {
            QPixmap pixmap = QPixmap::fromImage(image);
            
            // Cache the tile
            m_cache->putTile(key, pixmap);
            
            // Emit success signal
            emit tileLoaded(key.zoom, key.x, key.y, pixmap);
        } else {
            emit tileLoadFailed(key.zoom, key.x, key.y, "Failed to decode image");
        }
    } else {
        QString error = reply->errorString();
        qWarning() << "Tile download failed:" << error;
        emit tileLoadFailed(key.zoom, key.x, key.y, error);
    }
    
    reply->deleteLater();
}

void TileManager::processRequestQueue()
{
    QMutexLocker locker(&m_queueMutex);
    
    // Process requests while we have capacity
    while (m_activeRequests < m_maxConcurrentRequests && !m_requestQueue.isEmpty()) {
        // Sort queue by priority (simple bubble sort for small queues)
        if (m_requestQueue.size() > 1) {
            QList<TileRequest> requests;
            while (!m_requestQueue.isEmpty()) {
                requests.append(m_requestQueue.dequeue());
            }
            
            std::sort(requests.begin(), requests.end(), 
                     [](const TileRequest& a, const TileRequest& b) {
                         return a.priority > b.priority; // Higher priority first
                     });
            
            for (const auto& request : requests) {
                m_requestQueue.enqueue(request);
            }
        }
        
        TileRequest request = m_requestQueue.dequeue();
        
        // Check if tile is now cached (might have been loaded by another request)
        if (m_cache->hasTile(request.key)) {
            QPixmap pixmap = m_cache->getTile(request.key);
            if (!pixmap.isNull()) {
                emit tileLoaded(request.key.zoom, request.key.x, request.key.y, pixmap);
                continue;
            }
        }
        
        // Check if already in progress
        if (isRequestInProgress(request.key)) {
            continue;
        }
        
        // Start download
        startDownload(request.key);
    }
}

QString TileManager::getTileUrl(int zoom, int x, int y) const
{
    // TMS format: {baseUrl}/{zoom}/{x}/{y}.png
    return QString("%1/%2/%3/%4.png").arg(m_baseUrl).arg(zoom).arg(x).arg(y);
}

void TileManager::startDownload(const TileCache::TileKey& key)
{
    QString url = getTileUrl(key.zoom, key.x, key.y);
    
    QNetworkRequest request(url);
    request.setRawHeader("User-Agent", "Qt Map Application");
    request.setAttribute(QNetworkRequest::CacheLoadControlAttribute, 
                        QNetworkRequest::PreferCache);
    
    QNetworkReply* reply = m_networkManager->get(request);
    
    // Track the request
    {
        QMutexLocker locker(&m_activeDownloadsMutex);
        m_activeDownloads.insert(reply, key);
        m_activeRequests++;
    }
    
    // Connect signals
    connect(reply, &QNetworkReply::finished, this, &TileManager::onTileDownloaded);
}

bool TileManager::isRequestInProgress(const TileCache::TileKey& key) const
{
    QMutexLocker locker(&m_activeDownloadsMutex);
    for (auto it = m_activeDownloads.begin(); it != m_activeDownloads.end(); ++it) {
        if (it.value().zoom == key.zoom && it.value().x == key.x && it.value().y == key.y) {
            return true;
        }
    }
    return false;
}

// TileLoadTask implementation
TileLoadTask::TileLoadTask(TileManager* manager, const TileCache::TileKey& key)
    : m_manager(manager)
    , m_key(key)
{
    setAutoDelete(true);
}

void TileLoadTask::run()
{
    // This could be used for CPU-intensive tile processing
    // For now, network loading is handled in the main thread
}
