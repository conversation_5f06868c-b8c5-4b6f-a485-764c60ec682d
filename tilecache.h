#ifndef TILECACHE_H
#define TILECACHE_H

#include <QObject>
#include <QPixmap>
#include <QHash>
#include <QMutex>
#include <QDir>
#include <QStandardPaths>
#include <QTimer>
#include <QDateTime>

/**
 * @brief Multi-level tile caching system with memory and disk cache
 * 
 * Provides efficient caching of map tiles with:
 * - Memory cache for fast access to frequently used tiles
 * - Disk cache for persistent storage
 * - LRU eviction policy
 * - Thread-safe operations
 */
class TileCache : public QObject
{
    Q_OBJECT

public:
    struct TileKey {
        int zoom;
        int x;
        int y;
        
        bool operator==(const TileKey& other) const {
            return zoom == other.zoom && x == other.x && y == other.y;
        }
    };
    
    struct CacheEntry {
        QPixmap pixmap;
        QDateTime lastAccessed;
        qint64 size;
    };

    explicit TileCache(QObject *parent = nullptr);
    ~TileCache();
    
    /**
     * @brief Get tile from cache (memory first, then disk)
     * @param key Tile key (zoom, x, y)
     * @return Cached pixmap or null pixmap if not found
     */
    QPixmap getTile(const TileKey& key);
    
    /**
     * @brief Store tile in cache (both memory and disk)
     * @param key Tile key (zoom, x, y)
     * @param pixmap Tile pixmap to cache
     */
    void putTile(const TileKey& key, const QPixmap& pixmap);
    
    /**
     * @brief Check if tile exists in cache
     * @param key Tile key (zoom, x, y)
     * @return True if tile is cached
     */
    bool hasTile(const TileKey& key);
    
    /**
     * @brief Clear all cached tiles
     */
    void clear();
    
    /**
     * @brief Get current memory cache size in bytes
     */
    qint64 getMemoryCacheSize() const;
    
    /**
     * @brief Get current disk cache size in bytes
     */
    qint64 getDiskCacheSize() const;
    
    /**
     * @brief Set maximum memory cache size
     * @param maxSize Maximum size in bytes
     */
    void setMaxMemoryCacheSize(qint64 maxSize);
    
    /**
     * @brief Set maximum disk cache size
     * @param maxSize Maximum size in bytes
     */
    void setMaxDiskCacheSize(qint64 maxSize);

private slots:
    void cleanupCache();

private:
    QString getTilePath(const TileKey& key) const;
    QString getTileFileName(const TileKey& key) const;
    void ensureCacheDirectory();
    void evictMemoryCache();
    void evictDiskCache();
    qint64 calculateDiskCacheSize();
    
    // Memory cache
    QHash<TileKey, CacheEntry> m_memoryCache;
    mutable QMutex m_memoryCacheMutex;
    qint64 m_memoryCacheSize;
    qint64 m_maxMemoryCacheSize;
    
    // Disk cache
    QDir m_cacheDir;
    mutable QMutex m_diskCacheMutex;
    qint64 m_diskCacheSize;
    qint64 m_maxDiskCacheSize;
    
    // Cleanup timer
    QTimer* m_cleanupTimer;
    
    // Default cache sizes (in bytes)
    static constexpr qint64 DEFAULT_MEMORY_CACHE_SIZE = 100 * 1024 * 1024; // 100 MB
    static constexpr qint64 DEFAULT_DISK_CACHE_SIZE = 1024 * 1024 * 1024;  // 1 GB
};

// Hash function for TileKey
inline uint qHash(const TileCache::TileKey& key, uint seed = 0)
{
    return qHash(QString("%1_%2_%3").arg(key.zoom).arg(key.x).arg(key.y), seed);
}

Q_DECLARE_METATYPE(TileCache::TileKey)

#endif // TILECACHE_H
