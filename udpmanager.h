#ifndef UDPMANAGER_H
#define UDPMANAGER_H

#include <QObject>
#include <QUdpSocket>
#include <QJsonDocument>
#include <QJsonObject>
#include <QTimer>
#include <QHostAddress>

/**
 * @brief Manages UDP communication for target data and responses
 * 
 * Features:
 * - Receives JSON messages via UDP
 * - Parses target data from JSON
 * - Sends response messages when targets are clicked
 * - Robust error handling and connection management
 * - Configurable ports and addresses
 */
class UdpManager : public QObject
{
    Q_OBJECT

public:
    struct TargetData {
        QString id;
        QString targetType;
        double latitude;
        double longitude;
        double altitude;
        QString name;
        QString description;
        QJsonObject additionalData;
        
        bool isValid() const {
            return !id.isEmpty() && !targetType.isEmpty() && 
                   latitude >= -90.0 && latitude <= 90.0 &&
                   longitude >= -180.0 && longitude <= 180.0;
        }
    };

    explicit UdpManager(QObject *parent = nullptr);
    ~UdpManager();
    
    /**
     * @brief Start listening for UDP messages
     * @param port Port to listen on (default: 12345)
     * @param address Address to bind to (default: any)
     * @return True if successfully started
     */
    bool startListening(quint16 port = 12345, const QHostAddress& address = QHostAddress::Any);
    
    /**
     * @brief Stop listening for UDP messages
     */
    void stopListening();
    
    /**
     * @brief Send response message via UDP
     * @param targetId ID of the clicked target
     * @param responseData Additional response data
     * @param destinationAddress Destination address
     * @param destinationPort Destination port
     * @return True if message was sent successfully
     */
    bool sendTargetClickResponse(const QString& targetId, 
                               const QJsonObject& responseData = QJsonObject(),
                               const QHostAddress& destinationAddress = QHostAddress::LocalHost,
                               quint16 destinationPort = 12346);
    
    /**
     * @brief Send custom JSON message via UDP
     * @param message JSON message to send
     * @param destinationAddress Destination address
     * @param destinationPort Destination port
     * @return True if message was sent successfully
     */
    bool sendMessage(const QJsonObject& message,
                    const QHostAddress& destinationAddress = QHostAddress::LocalHost,
                    quint16 destinationPort = 12346);
    
    /**
     * @brief Check if currently listening
     * @return True if listening for messages
     */
    bool isListening() const;
    
    /**
     * @brief Get current listening port
     * @return Port number or 0 if not listening
     */
    quint16 getListeningPort() const;
    
    /**
     * @brief Set connection timeout for error detection
     * @param timeoutMs Timeout in milliseconds
     */
    void setConnectionTimeout(int timeoutMs);

signals:
    /**
     * @brief Emitted when valid target data is received
     * @param targetData Parsed target data
     */
    void targetDataReceived(const TargetData& targetData);
    
    /**
     * @brief Emitted when target update is received
     * @param targetData Updated target data
     */
    void targetUpdateReceived(const TargetData& targetData);
    
    /**
     * @brief Emitted when target removal is requested
     * @param targetId ID of target to remove
     */
    void targetRemovalRequested(const QString& targetId);
    
    /**
     * @brief Emitted when a UDP error occurs
     * @param error Error message
     */
    void udpError(const QString& error);
    
    /**
     * @brief Emitted when connection status changes
     * @param connected True if connected/listening
     */
    void connectionStatusChanged(bool connected);

private slots:
    void onDatagramReceived();
    void onSocketError(QAbstractSocket::SocketError error);
    void checkConnectionHealth();

private:
    bool parseTargetMessage(const QJsonObject& json, TargetData& targetData);
    void handleTargetMessage(const QJsonObject& json);
    void handleControlMessage(const QJsonObject& json);
    QJsonObject createResponseMessage(const QString& targetId, const QJsonObject& additionalData);
    
    // Network components
    QUdpSocket* m_udpSocket;
    quint16 m_listeningPort;
    bool m_isListening;
    
    // Connection monitoring
    QTimer* m_connectionTimer;
    int m_connectionTimeout;
    QDateTime m_lastMessageTime;
    
    // Statistics
    qint64 m_messagesReceived;
    qint64 m_messagesSent;
    qint64 m_parseErrors;
    
    // Default ports
    static constexpr quint16 DEFAULT_LISTEN_PORT = 12345;
    static constexpr quint16 DEFAULT_SEND_PORT = 12346;
    static constexpr int DEFAULT_CONNECTION_TIMEOUT = 30000; // 30 seconds
};

Q_DECLARE_METATYPE(UdpManager::TargetData)

#endif // UDPMANAGER_H
