#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QStatusBar>
#include <QLabel>
#include <QTimer>
#include <QHash>
#include "mapwidget.h"
#include "udpmanager.h"
#include "targetdetaildialog.h"

QT_BEGIN_NAMESPACE
namespace Ui { class MainWindow; }
QT_END_NAMESPACE

/**
 * @brief Main application window
 *
 * Integrates all components:
 * - Map widget with tile rendering
 * - UDP communication for target data
 * - Target management and display
 * - Status monitoring and error handling
 */
class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

private slots:
    void onTargetDataReceived(const UdpManager::TargetData& targetData);
    void onTargetUpdateReceived(const UdpManager::TargetData& targetData);
    void onTargetRemovalRequested(const QString& targetId);
    void onTargetClicked(Target* target);
    void onMouseCoordinatesChanged(const QPointF& coordinates);
    void onUdpError(const QString& error);
    void onConnectionStatusChanged(bool connected);
    void updateStatusBar();

private:
    void setupUI();
    void setupStatusBar();
    void setupConnections();
    void showTargetDetailDialog(Target* target);
    void updateWindowTitle();

    Ui::MainWindow *ui;

    // Core components
    MapWidget* m_mapWidget;
    UdpManager* m_udpManager;

    // UI components
    QLabel* m_coordinateStatusLabel;
    QLabel* m_connectionStatusLabel;
    QLabel* m_targetCountLabel;
    QLabel* m_zoomStatusLabel;

    // Status monitoring
    QTimer* m_statusUpdateTimer;

    // Target management
    QHash<QString, TargetDetailDialog*> m_targetDialogs;

    // Settings
    static constexpr quint16 UDP_LISTEN_PORT = 12345;
    static constexpr int STATUS_UPDATE_INTERVAL = 1000; // ms
};

#endif // MAINWINDOW_H
