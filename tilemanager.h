#ifndef TILEMANAGER_H
#define TILEMANAGER_H

#include <QObject>
#include <QNetworkAccessManager>
#include <QNetworkReply>
#include <QPixmap>
#include <QTimer>
#include <QQueue>
#include <QMutex>
#include <QThreadPool>
#include <QRunnable>
#include <QRect>
#include "tilecache.h"

/**
 * @brief Manages tile loading from TMS server with caching and async loading
 * 
 * Features:
 * - Asynchronous tile loading from localhost:8080
 * - TMS format support
 * - Multi-level caching (memory + disk)
 * - Viewport-aware loading
 * - Request queuing and prioritization
 * - Network error handling and retries
 */
class TileManager : public QObject
{
    Q_OBJECT

public:
    struct TileRequest {
        TileCache::TileKey key;
        int priority;
        QDateTime requestTime;
        
        bool operator<(const TileRequest& other) const {
            return priority < other.priority;
        }
    };

    explicit TileManager(QObject *parent = nullptr);
    ~TileManager();
    
    /**
     * @brief Request a tile asynchronously
     * @param zoom Zoom level
     * @param x Tile X coordinate
     * @param y Tile Y coordinate
     * @param priority Request priority (higher = more important)
     */
    void requestTile(int zoom, int x, int y, int priority = 0);
    
    /**
     * @brief Request tiles for a viewport area
     * @param viewport Viewport rectangle in tile coordinates
     * @param zoom Zoom level
     */
    void requestTilesForViewport(const QRect& viewport, int zoom);
    
    /**
     * @brief Get tile from cache (synchronous)
     * @param zoom Zoom level
     * @param x Tile X coordinate
     * @param y Tile Y coordinate
     * @return Cached tile or null pixmap if not available
     */
    QPixmap getTile(int zoom, int x, int y);
    
    /**
     * @brief Check if tile is available in cache
     * @param zoom Zoom level
     * @param x Tile X coordinate
     * @param y Tile Y coordinate
     * @return True if tile is cached
     */
    bool hasTile(int zoom, int x, int y);
    
    /**
     * @brief Set the base URL for tile server
     * @param baseUrl Base URL (default: http://localhost:8080)
     */
    void setBaseUrl(const QString& baseUrl);
    
    /**
     * @brief Set maximum concurrent requests
     * @param maxRequests Maximum number of concurrent tile requests
     */
    void setMaxConcurrentRequests(int maxRequests);
    
    /**
     * @brief Clear all cached tiles
     */
    void clearCache();
    
    /**
     * @brief Get cache statistics
     */
    QPair<qint64, qint64> getCacheSize() const; // memory, disk

signals:
    /**
     * @brief Emitted when a tile is loaded and ready
     * @param zoom Zoom level
     * @param x Tile X coordinate
     * @param y Tile Y coordinate
     * @param pixmap Loaded tile pixmap
     */
    void tileLoaded(int zoom, int x, int y, const QPixmap& pixmap);
    
    /**
     * @brief Emitted when a tile fails to load
     * @param zoom Zoom level
     * @param x Tile X coordinate
     * @param y Tile Y coordinate
     * @param error Error message
     */
    void tileLoadFailed(int zoom, int x, int y, const QString& error);

private slots:
    void onTileDownloaded();
    void processRequestQueue();

private:
    QString getTileUrl(int zoom, int x, int y) const;
    void startDownload(const TileCache::TileKey& key);
    bool isRequestInProgress(const TileCache::TileKey& key) const;
    
    // Network management
    QNetworkAccessManager* m_networkManager;
    QString m_baseUrl;
    int m_maxConcurrentRequests;
    int m_activeRequests;
    
    // Request queue
    QQueue<TileRequest> m_requestQueue;
    QMutex m_queueMutex;
    QTimer* m_queueTimer;
    
    // Active requests tracking
    QHash<QNetworkReply*, TileCache::TileKey> m_activeDownloads;
    QMutex m_activeDownloadsMutex;
    
    // Caching
    TileCache* m_cache;
    
    // Constants
    static constexpr int DEFAULT_MAX_CONCURRENT_REQUESTS = 6;
    static constexpr int QUEUE_PROCESS_INTERVAL = 50; // ms
    static constexpr int MAX_RETRIES = 3;
    static constexpr int RETRY_DELAY = 1000; // ms
};

/**
 * @brief Runnable task for tile loading
 */
class TileLoadTask : public QRunnable
{
public:
    TileLoadTask(TileManager* manager, const TileCache::TileKey& key);
    void run() override;

private:
    TileManager* m_manager;
    TileCache::TileKey m_key;
};

#endif // TILEMANAGER_H
