#ifndef TARGET_H
#define TARGET_H

#include <QObject>
#include <QPixmap>
#include <QPointF>
#include <QRect>
#include <QDateTime>
#include <QJsonObject>
#include <QPropertyAnimation>
#include <QGraphicsEffect>
#include "udpmanager.h"

/**
 * @brief Represents a dynamic target on the map with icon and interaction
 * 
 * Features:
 * - Dynamic icon loading based on target type
 * - Real-time position updates
 * - Click detection and interaction
 * - Animation support for smooth updates
 * - Visual feedback for selection/hover states
 */
class Target : public QObject
{
    Q_OBJECT
    Q_PROPERTY(QPointF position READ position WRITE setPosition NOTIFY positionChanged)
    Q_PROPERTY(qreal opacity READ opacity WRITE setOpacity NOTIFY opacityChanged)

public:
    enum TargetState {
        Normal,
        Hovered,
        Selected,
        Highlighted
    };

    explicit Target(const UdpManager::TargetData& data, QObject *parent = nullptr);
    ~Target();
    
    /**
     * @brief Get target ID
     */
    QString getId() const { return m_data.id; }
    
    /**
     * @brief Get target type
     */
    QString getTargetType() const { return m_data.targetType; }
    
    /**
     * @brief Get target data
     */
    const UdpManager::TargetData& getData() const { return m_data; }
    
    /**
     * @brief Update target data
     * @param data New target data
     */
    void updateData(const UdpManager::TargetData& data);
    
    /**
     * @brief Get current position (lat/lon)
     */
    QPointF position() const { return QPointF(m_data.longitude, m_data.latitude); }
    
    /**
     * @brief Set position with animation
     * @param position New position (lat/lon)
     * @param animated Whether to animate the transition
     */
    void setPosition(const QPointF& position, bool animated = true);
    
    /**
     * @brief Get screen position in pixels
     */
    QPointF getScreenPosition() const { return m_screenPosition; }
    
    /**
     * @brief Set screen position in pixels
     */
    void setScreenPosition(const QPointF& screenPos);
    
    /**
     * @brief Get target icon
     */
    QPixmap getIcon() const { return m_icon; }
    
    /**
     * @brief Get icon size
     */
    QSize getIconSize() const { return m_iconSize; }
    
    /**
     * @brief Set icon size
     */
    void setIconSize(const QSize& size);
    
    /**
     * @brief Get bounding rectangle for click detection
     */
    QRect getBoundingRect() const;
    
    /**
     * @brief Check if point is inside target
     * @param point Point in screen coordinates
     * @return True if point hits the target
     */
    bool contains(const QPointF& point) const;
    
    /**
     * @brief Get current state
     */
    TargetState getState() const { return m_state; }
    
    /**
     * @brief Set target state
     */
    void setState(TargetState state);
    
    /**
     * @brief Get opacity
     */
    qreal opacity() const { return m_opacity; }
    
    /**
     * @brief Set opacity
     */
    void setOpacity(qreal opacity);
    
    /**
     * @brief Check if target is visible
     */
    bool isVisible() const { return m_visible; }
    
    /**
     * @brief Set visibility
     */
    void setVisible(bool visible);
    
    /**
     * @brief Get last update time
     */
    QDateTime getLastUpdateTime() const { return m_lastUpdateTime; }
    
    /**
     * @brief Start blinking animation
     */
    void startBlinking();
    
    /**
     * @brief Stop blinking animation
     */
    void stopBlinking();

signals:
    /**
     * @brief Emitted when target is clicked
     * @param target Pointer to the clicked target
     */
    void clicked(Target* target);
    
    /**
     * @brief Emitted when position changes
     */
    void positionChanged();
    
    /**
     * @brief Emitted when opacity changes
     */
    void opacityChanged();
    
    /**
     * @brief Emitted when target state changes
     */
    void stateChanged(TargetState newState);
    
    /**
     * @brief Emitted when target needs to be repainted
     */
    void needsRepaint();

private slots:
    void onPositionAnimationFinished();
    void onBlinkAnimationFinished();

private:
    void loadIcon();
    QString getIconPath() const;
    void setupAnimations();
    
    // Target data
    UdpManager::TargetData m_data;
    QDateTime m_lastUpdateTime;
    
    // Visual properties
    QPixmap m_icon;
    QSize m_iconSize;
    QPointF m_screenPosition;
    TargetState m_state;
    qreal m_opacity;
    bool m_visible;
    
    // Animations
    QPropertyAnimation* m_positionAnimation;
    QPropertyAnimation* m_opacityAnimation;
    QPropertyAnimation* m_blinkAnimation;
    
    // Icon cache
    static QHash<QString, QPixmap> s_iconCache;
    
    // Default settings
    static constexpr int DEFAULT_ICON_SIZE = 32;
    static constexpr int ANIMATION_DURATION = 300; // ms
    static constexpr qreal HOVER_OPACITY = 0.8;
    static constexpr qreal SELECTED_OPACITY = 1.0;
    static constexpr qreal NORMAL_OPACITY = 0.9;
};

#endif // TARGET_H
