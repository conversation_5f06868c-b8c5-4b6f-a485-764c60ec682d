#!/usr/bin/env python3
"""
Create basic PNG icons for the map application
"""

try:
    from PIL import Image, ImageDraw
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

import os

def create_target_icon(color, size=32):
    """Create a target icon with the specified color."""
    if not PIL_AVAILABLE:
        return None
    
    # Create image with transparency
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Draw target circles
    center = size // 2
    
    # Outer circle
    draw.ellipse([2, 2, size-2, size-2], outline=color, width=2)
    
    # Inner circle
    inner_size = size // 3
    inner_offset = (size - inner_size) // 2
    draw.ellipse([inner_offset, inner_offset, inner_offset + inner_size, inner_offset + inner_size], 
                fill=color)
    
    # Cross lines
    draw.line([center, 0, center, size], fill=color, width=2)
    draw.line([0, center, size, center], fill=color, width=2)
    
    return img

def create_ui_icon(icon_type, size=16):
    """Create a UI icon."""
    if not PIL_AVAILABLE:
        return None
    
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    if icon_type == 'track':
        # Create a simple crosshair icon
        center = size // 2
        draw.line([center, 2, center, size-2], fill='black', width=2)
        draw.line([2, center, size-2, center], fill='black', width=2)
        draw.ellipse([center-3, center-3, center+3, center+3], outline='black', width=1)
    
    elif icon_type == 'send':
        # Create a simple arrow icon
        points = [(2, size//2), (size-4, 2), (size-4, size//3), 
                 (size-2, size//3), (size-2, 2*size//3), (size-4, 2*size//3), (size-4, size-2)]
        draw.polygon(points, fill='black')
    
    return img

def create_fallback_icon(filename, color_name):
    """Create a simple colored square as fallback."""
    size = 32
    colors = {
        'red': (255, 0, 0, 255),
        'blue': (0, 0, 255, 255),
        'black': (0, 0, 0, 255)
    }
    
    color = colors.get(color_name, (128, 128, 128, 255))
    
    # Create simple text file as placeholder
    with open(filename, 'w') as f:
        f.write(f"# Placeholder for {color_name} icon\n")
        f.write(f"# This should be a {size}x{size} PNG file\n")
        f.write(f"# Color: {color}\n")

def main():
    """Create all required icons."""
    icons_dir = 'icons'
    
    # Ensure icons directory exists
    os.makedirs(icons_dir, exist_ok=True)
    
    if PIL_AVAILABLE:
        print("Creating PNG icons with PIL...")
        
        # Create target icons
        red_icon = create_target_icon('red')
        if red_icon:
            red_icon.save(os.path.join(icons_dir, 'red.png'))
            print("Created red.png")
        
        blue_icon = create_target_icon('blue')
        if blue_icon:
            blue_icon.save(os.path.join(icons_dir, 'blue.png'))
            print("Created blue.png")
        
        # Create UI icons
        track_icon = create_ui_icon('track')
        if track_icon:
            track_icon.save(os.path.join(icons_dir, 'track.png'))
            print("Created track.png")
        
        send_icon = create_ui_icon('send')
        if send_icon:
            send_icon.save(os.path.join(icons_dir, 'send.png'))
            print("Created send.png")
    
    else:
        print("PIL not available, creating placeholder files...")
        
        # Create placeholder files
        create_fallback_icon(os.path.join(icons_dir, 'red.png'), 'red')
        create_fallback_icon(os.path.join(icons_dir, 'blue.png'), 'blue')
        create_fallback_icon(os.path.join(icons_dir, 'track.png'), 'black')
        create_fallback_icon(os.path.join(icons_dir, 'send.png'), 'black')
        
        print("Created placeholder files")
        print("Note: Install PIL/Pillow to create actual PNG icons: pip install Pillow")
    
    print("Icon creation completed!")

if __name__ == "__main__":
    main()
