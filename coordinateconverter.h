#ifndef COORDINATECONVERTER_H
#define COORDINATECONVERTER_H

#include <QPointF>
#include <cmath>

/**
 * @brief Coordinate conversion utilities for GCJ02 Web Mercator projection
 * 
 * This class provides conversion between geographic coordinates (lat/lon) and
 * Web Mercator projection coordinates, specifically handling GCJ02 coordinate system
 * used in Chinese mapping services.
 */
class CoordinateConverter
{
public:
    // Earth radius in meters
    static constexpr double EARTH_RADIUS = 6378137.0;
    
    // Web Mercator bounds
    static constexpr double MAX_LATITUDE = 85.0511287798;
    static constexpr double MIN_LATITUDE = -85.0511287798;
    static constexpr double MAX_LONGITUDE = 180.0;
    static constexpr double MIN_LONGITUDE = -180.0;
    
    /**
     * @brief Convert latitude/longitude to Web Mercator coordinates
     * @param lat Latitude in degrees
     * @param lon Longitude in degrees
     * @return QPointF with x=easting, y=northing in meters
     */
    static QPointF latLonToWebMercator(double lat, double lon);
    
    /**
     * @brief Convert Web Mercator coordinates to latitude/longitude
     * @param x Easting in meters
     * @param y Northing in meters
     * @return QPointF with x=longitude, y=latitude in degrees
     */
    static QPointF webMercatorToLatLon(double x, double y);
    
    /**
     * @brief Convert Web Mercator coordinates to tile coordinates
     * @param x Easting in meters
     * @param y Northing in meters
     * @param zoom Zoom level
     * @return QPointF with x=tile_x, y=tile_y
     */
    static QPointF webMercatorToTile(double x, double y, int zoom);
    
    /**
     * @brief Convert tile coordinates to Web Mercator coordinates
     * @param tileX Tile X coordinate
     * @param tileY Tile Y coordinate
     * @param zoom Zoom level
     * @return QPointF with x=easting, y=northing in meters
     */
    static QPointF tileToWebMercator(double tileX, double tileY, int zoom);
    
    /**
     * @brief Convert latitude/longitude directly to tile coordinates
     * @param lat Latitude in degrees
     * @param lon Longitude in degrees
     * @param zoom Zoom level
     * @return QPointF with x=tile_x, y=tile_y
     */
    static QPointF latLonToTile(double lat, double lon, int zoom);
    
    /**
     * @brief Convert tile coordinates directly to latitude/longitude
     * @param tileX Tile X coordinate
     * @param tileY Tile Y coordinate
     * @param zoom Zoom level
     * @return QPointF with x=longitude, y=latitude in degrees
     */
    static QPointF tileToLatLon(double tileX, double tileY, int zoom);
    
    /**
     * @brief Get the resolution (meters per pixel) at a given zoom level
     * @param zoom Zoom level
     * @return Resolution in meters per pixel
     */
    static double getResolution(int zoom);
    
    /**
     * @brief Clamp latitude to valid Web Mercator bounds
     * @param lat Latitude in degrees
     * @return Clamped latitude
     */
    static double clampLatitude(double lat);
    
    /**
     * @brief Clamp longitude to valid bounds
     * @param lon Longitude in degrees
     * @return Clamped longitude
     */
    static double clampLongitude(double lon);

private:
    CoordinateConverter() = delete; // Static class
};

#endif // COORDINATECONVERTER_H
