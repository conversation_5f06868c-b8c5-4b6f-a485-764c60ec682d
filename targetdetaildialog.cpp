#include "targetdetaildialog.h"
#include "ui_targetdetaildialog.h"
#include <QClipboard>
#include <QApplication>
#include <QJsonDocument>
#include <QMessageBox>
#include <QPixmap>
#include <QIcon>
#include <QDateTime>

TargetDetailDialog::TargetDetailDialog(const UdpManager::TargetData& targetData, QWidget *parent)
    : QDialog(parent)
    , ui(new Ui::TargetDetailDialog)
    , m_targetData(targetData)
{
    ui->setupUi(this);
    setupUI();
    populateFields();
}

TargetDetailDialog::~TargetDetailDialog()
{
    delete ui;
}

void TargetDetailDialog::updateTargetData(const UdpManager::TargetData& targetData)
{
    m_targetData = targetData;
    populateFields();
}

QString TargetDetailDialog::getTargetId() const
{
    return m_targetData.id;
}

void TargetDetailDialog::setupUI()
{
    setWindowTitle(QString("Target Details - %1").arg(m_targetData.id));
    setMinimumSize(450, 600);
    
    // Create main layout for scroll area content
    QWidget* contentWidget = ui->scrollArea->widget();
    QVBoxLayout* mainLayout = new QVBoxLayout(contentWidget);
    
    // Icon and basic info group
    QGroupBox* basicGroup = new QGroupBox("Basic Information");
    QFormLayout* basicLayout = new QFormLayout(basicGroup);
    
    // Icon
    m_iconLabel = new QLabel();
    m_iconLabel->setFixedSize(48, 48);
    m_iconLabel->setScaledContents(true);
    m_iconLabel->setStyleSheet("border: 1px solid gray;");
    basicLayout->addRow("Icon:", m_iconLabel);
    
    // Basic fields
    m_idEdit = new QLineEdit();
    m_idEdit->setReadOnly(true);
    basicLayout->addRow("ID:", m_idEdit);
    
    m_typeEdit = new QLineEdit();
    m_typeEdit->setReadOnly(true);
    basicLayout->addRow("Type:", m_typeEdit);
    
    m_nameEdit = new QLineEdit();
    m_nameEdit->setReadOnly(true);
    basicLayout->addRow("Name:", m_nameEdit);
    
    mainLayout->addWidget(basicGroup);
    
    // Position group
    QGroupBox* positionGroup = new QGroupBox("Position");
    QFormLayout* positionLayout = new QFormLayout(positionGroup);
    
    m_latitudeEdit = new QLineEdit();
    m_latitudeEdit->setReadOnly(true);
    positionLayout->addRow("Latitude:", m_latitudeEdit);
    
    m_longitudeEdit = new QLineEdit();
    m_longitudeEdit->setReadOnly(true);
    positionLayout->addRow("Longitude:", m_longitudeEdit);
    
    m_altitudeEdit = new QLineEdit();
    m_altitudeEdit->setReadOnly(true);
    positionLayout->addRow("Altitude:", m_altitudeEdit);
    
    // Copy coordinates button
    m_copyCoordinatesButton = new QPushButton("Copy Coordinates");
    connect(m_copyCoordinatesButton, &QPushButton::clicked, 
            this, &TargetDetailDialog::onCopyCoordinatesClicked);
    positionLayout->addRow("", m_copyCoordinatesButton);
    
    mainLayout->addWidget(positionGroup);
    
    // Description group
    QGroupBox* descGroup = new QGroupBox("Description");
    QVBoxLayout* descLayout = new QVBoxLayout(descGroup);
    
    m_descriptionEdit = new QTextEdit();
    m_descriptionEdit->setReadOnly(true);
    m_descriptionEdit->setMaximumHeight(80);
    descLayout->addWidget(m_descriptionEdit);
    
    mainLayout->addWidget(descGroup);
    
    // Additional data group
    QGroupBox* dataGroup = new QGroupBox("Additional Data");
    QVBoxLayout* dataLayout = new QVBoxLayout(dataGroup);
    
    m_additionalDataEdit = new QTextEdit();
    m_additionalDataEdit->setReadOnly(true);
    m_additionalDataEdit->setMaximumHeight(120);
    m_additionalDataEdit->setFont(QFont("Courier", 9));
    dataLayout->addWidget(m_additionalDataEdit);
    
    mainLayout->addWidget(dataGroup);
    
    // Status group
    QGroupBox* statusGroup = new QGroupBox("Status");
    QFormLayout* statusLayout = new QFormLayout(statusGroup);
    
    m_lastUpdateLabel = new QLabel();
    statusLayout->addRow("Last Update:", m_lastUpdateLabel);
    
    mainLayout->addWidget(statusGroup);
    
    // Action buttons
    QGroupBox* actionGroup = new QGroupBox("Actions");
    QHBoxLayout* actionLayout = new QHBoxLayout(actionGroup);
    
    m_trackButton = new QPushButton("Track Target");
    m_trackButton->setIcon(QIcon(":/icons/track.png"));
    connect(m_trackButton, &QPushButton::clicked,
            this, &TargetDetailDialog::onTrackButtonClicked);
    actionLayout->addWidget(m_trackButton);

    m_sendResponseButton = new QPushButton("Send Response");
    m_sendResponseButton->setIcon(QIcon(":/icons/send.png"));
    connect(m_sendResponseButton, &QPushButton::clicked,
            this, &TargetDetailDialog::onSendResponseClicked);
    actionLayout->addWidget(m_sendResponseButton);
    
    mainLayout->addWidget(actionGroup);
    
    // Add stretch
    mainLayout->addStretch();
}

void TargetDetailDialog::populateFields()
{
    // Basic information
    m_idEdit->setText(m_targetData.id);
    m_typeEdit->setText(m_targetData.targetType);
    m_nameEdit->setText(m_targetData.name.isEmpty() ? "N/A" : m_targetData.name);

    // Position
    m_latitudeEdit->setText(formatCoordinate(m_targetData.latitude, true));
    m_longitudeEdit->setText(formatCoordinate(m_targetData.longitude, false));
    m_altitudeEdit->setText(QString("%1 m").arg(m_targetData.altitude, 0, 'f', 1));

    // Description
    m_descriptionEdit->setPlainText(m_targetData.description.isEmpty() ? "No description available" : m_targetData.description);

    // Additional data (formatted JSON)
    QJsonDocument doc(m_targetData.additionalData);
    QString jsonText = doc.toJson(QJsonDocument::Indented);
    m_additionalDataEdit->setPlainText(jsonText);

    // Status
    m_lastUpdateLabel->setText(formatDateTime(QDateTime::currentDateTime()));

    // Load icon (simplified for now)
    QPixmap icon(32, 32);
    if (m_targetData.targetType.toLower().contains("red") ||
        m_targetData.targetType.toLower().contains("enemy")) {
        icon.fill(Qt::red);
    } else {
        icon.fill(Qt::blue);
    }
    m_iconLabel->setPixmap(icon);
}

void TargetDetailDialog::onTrackButtonClicked()
{
    emit trackTargetRequested(m_targetData.id);
    QMessageBox::information(this, "Track Target",
                           QString("Tracking request sent for target: %1").arg(m_targetData.id));
}

void TargetDetailDialog::onSendResponseClicked()
{
    QJsonObject responseData;
    responseData["action"] = "target_clicked";
    responseData["user_action"] = "manual_response";
    responseData["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);

    emit sendResponseRequested(m_targetData.id, responseData);
    QMessageBox::information(this, "Response Sent",
                           QString("Response sent for target: %1").arg(m_targetData.id));
}

void TargetDetailDialog::onCopyCoordinatesClicked()
{
    QString coordinates = QString("%1, %2").arg(m_targetData.latitude, 0, 'f', 6)
                                          .arg(m_targetData.longitude, 0, 'f', 6);

    QClipboard* clipboard = QApplication::clipboard();
    clipboard->setText(coordinates);

    QMessageBox::information(this, "Coordinates Copied",
                           QString("Coordinates copied to clipboard:\n%1").arg(coordinates));
}

QString TargetDetailDialog::formatCoordinate(double value, bool isLatitude) const
{
    QString direction;
    if (isLatitude) {
        direction = (value >= 0) ? "N" : "S";
    } else {
        direction = (value >= 0) ? "E" : "W";
    }

    return QString("%1° %2").arg(qAbs(value), 0, 'f', 6).arg(direction);
}

QString TargetDetailDialog::formatDateTime(const QDateTime& dateTime) const
{
    return dateTime.toString("yyyy-MM-dd hh:mm:ss");
}
