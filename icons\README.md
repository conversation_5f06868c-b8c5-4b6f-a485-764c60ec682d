# Icons Directory

This directory should contain the following icon files:

- `red.png` - Red target icon (32x32 pixels recommended)
- `blue.png` - Blue target icon (32x32 pixels recommended)
- `track.png` - Track button icon (16x16 pixels recommended)
- `send.png` - Send button icon (16x16 pixels recommended)

The application will create default colored squares if these files are not found.

## Icon Requirements

- Format: PNG with transparency support
- Red icon: Used for enemy/hostile/threat targets
- Blue icon: Used for friendly/neutral/safe targets
- Icons should be clearly visible on map backgrounds
- Recommended size: 32x32 pixels for target icons, 16x16 for UI icons
