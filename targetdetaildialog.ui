<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>TargetDetailDialog</class>
 <widget class="QDialog" name="TargetDetailDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>450</width>
    <height>600</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Target Details</string>
  </property>
  <property name="modal">
   <bool>true</bool>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QScrollArea" name="scrollArea">
     <property name="widgetResizable">
      <bool>true</bool>
     </property>
     <widget class="QWidget" name="scrollAreaWidgetContents">
      <property name="geometry">
       <rect>
        <x>0</x>
        <y>0</y>
        <width>430</width>
        <height>550</height>
       </rect>
      </property>
     </widget>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="buttonLayout">
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="closeButton">
       <property name="text">
        <string>Close</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections>
  <connection>
   <sender>closeButton</sender>
   <signal>clicked()</signal>
   <receiver>TargetDetailDialog</receiver>
   <slot>accept()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>389</x>
     <y>575</y>
    </hint>
    <hint type="destinationlabel">
     <x>224</x>
     <y>299</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
