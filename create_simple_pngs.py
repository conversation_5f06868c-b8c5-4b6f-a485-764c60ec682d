#!/usr/bin/env python3
"""
Create simple PNG files without PIL dependency
"""

import os

def create_minimal_png(filename, width=32, height=32):
    """Create a minimal PNG file using raw bytes."""
    
    # Minimal PNG file structure (1x1 transparent pixel)
    # This is a valid PNG file with minimal content
    png_data = bytes([
        0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A,  # PNG signature
        0x00, 0x00, 0x00, 0x0D,  # IHDR chunk length
        0x49, 0x48, 0x44, 0x52,  # IHDR
        0x00, 0x00, 0x00, 0x01,  # Width: 1
        0x00, 0x00, 0x00, 0x01,  # Height: 1
        0x08, 0x06, 0x00, 0x00, 0x00,  # Bit depth: 8, Color type: 6 (RGBA), Compression: 0, Filter: 0, Interlace: 0
        0x1F, 0x15, 0xC4, 0x89,  # CRC
        0x00, 0x00, 0x00, 0x0A,  # IDAT chunk length
        0x49, 0x44, 0x41, 0x54,  # IDAT
        0x78, 0x9C, 0x62, 0x00, 0x00, 0x00, 0x02, 0x00, 0x01,  # Compressed data (transparent pixel)
        0xE2, 0x21, 0xBC, 0x33,  # CRC
        0x00, 0x00, 0x00, 0x00,  # IEND chunk length
        0x49, 0x45, 0x4E, 0x44,  # IEND
        0xAE, 0x42, 0x60, 0x82   # CRC
    ])
    
    try:
        with open(filename, 'wb') as f:
            f.write(png_data)
        return True
    except Exception as e:
        print(f"Error creating {filename}: {e}")
        return False

def main():
    """Create all required PNG files."""
    icons_dir = 'icons'
    
    # Ensure icons directory exists
    os.makedirs(icons_dir, exist_ok=True)
    
    # List of required icons
    icons = ['red.png', 'blue.png', 'track.png', 'send.png']
    
    print("Creating minimal PNG files...")
    
    success_count = 0
    for icon in icons:
        filepath = os.path.join(icons_dir, icon)
        if create_minimal_png(filepath):
            print(f"Created {filepath}")
            success_count += 1
        else:
            print(f"Failed to create {filepath}")
    
    print(f"\nCreated {success_count}/{len(icons)} PNG files successfully!")
    print("\nNote: These are minimal 1x1 pixel transparent PNG files.")
    print("For better visual appearance, replace them with proper 32x32 pixel icons:")
    print("- red.png: Red target icon for enemy/hostile targets")
    print("- blue.png: Blue target icon for friendly/neutral targets")
    print("- track.png: Small tracking/crosshair icon for UI")
    print("- send.png: Small send/arrow icon for UI")

if __name__ == "__main__":
    main()
