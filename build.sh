#!/bin/bash
# Build script for Professional Map Application
# Requires Qt 5.14 or later with qmake in PATH

echo "Building Professional Map Application..."
echo

# Check if qmake is available
if ! command -v qmake &> /dev/null; then
    echo "Error: qmake not found in PATH"
    echo "Please ensure Qt 5.14 or later is installed and qmake is in your PATH"
    exit 1
fi

# Clean previous build
if [ -f Makefile ]; then
    echo "Cleaning previous build..."
    make clean > /dev/null 2>&1
    rm -f Makefile
fi

# Generate Makefile
echo "Generating Makefile..."
qmake map.pro
if [ $? -ne 0 ]; then
    echo "Error: Failed to generate Makefile"
    exit 1
fi

# Build the application
echo "Building application..."
make -j$(nproc)
if [ $? -ne 0 ]; then
    echo "Error: Build failed"
    exit 1
fi

echo
echo "Build completed successfully!"
echo
echo "To run the application:"
echo "  1. Start the tile server: python3 tile_server.py"
echo "  2. Run the application: ./map"
echo "  3. Test UDP: python3 test_udp.py"
echo

# Make the script executable
chmod +x build.sh
