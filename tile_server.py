#!/usr/bin/env python3
"""
Simple TMS Tile Server for Testing

This script creates a simple HTTP server that serves map tiles
in TMS format for testing the map application.
"""

import http.server
import socketserver
import os
import sys
from PIL import Image, ImageDraw, ImageFont
import io

class TileHandler(http.server.BaseHTTPRequestHandler):
    """HTTP handler for serving map tiles."""
    
    def do_GET(self):
        """Handle GET requests for tiles."""
        path = self.path.strip('/')
        
        # Parse TMS path: zoom/x/y.png
        try:
            parts = path.split('/')
            if len(parts) == 3 and parts[2].endswith('.png'):
                zoom = int(parts[0])
                x = int(parts[1])
                y = int(parts[2][:-4])  # Remove .png extension
                
                # Generate tile
                tile_data = self.generate_tile(zoom, x, y)
                
                # Send response
                self.send_response(200)
                self.send_header('Content-Type', 'image/png')
                self.send_header('Content-Length', str(len(tile_data)))
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()
                self.wfile.write(tile_data)
                
                print(f"Served tile: {zoom}/{x}/{y}")
                return
        except (ValueError, IndexError):
            pass
        
        # Invalid request
        self.send_response(404)
        self.send_header('Content-Type', 'text/plain')
        self.end_headers()
        self.wfile.write(b'Tile not found')
        print(f"Invalid tile request: {path}")
    
    def generate_tile(self, zoom, x, y):
        """Generate a test tile image."""
        # Create 256x256 tile
        size = 256
        image = Image.new('RGB', (size, size), color='lightblue')
        draw = ImageDraw.Draw(image)
        
        # Draw grid
        grid_size = 32
        for i in range(0, size, grid_size):
            draw.line([(i, 0), (i, size)], fill='white', width=1)
            draw.line([(0, i), (size, i)], fill='white', width=1)
        
        # Draw tile coordinates
        try:
            # Try to use a default font
            font = ImageFont.load_default()
        except:
            font = None
        
        text = f"Z:{zoom}\nX:{x}\nY:{y}"
        
        # Calculate text position (center)
        if font:
            bbox = draw.textbbox((0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
        else:
            text_width = len(text.split('\n')[0]) * 6  # Approximate
            text_height = len(text.split('\n')) * 11
        
        text_x = (size - text_width) // 2
        text_y = (size - text_height) // 2
        
        # Draw text background
        draw.rectangle([text_x - 5, text_y - 5, text_x + text_width + 5, text_y + text_height + 5], 
                      fill='white', outline='black')
        
        # Draw text
        draw.text((text_x, text_y), text, fill='black', font=font)
        
        # Add some visual variety based on coordinates
        if (x + y) % 2 == 0:
            # Add some diagonal lines for variety
            draw.line([(0, 0), (size, size)], fill='lightgray', width=2)
            draw.line([(0, size), (size, 0)], fill='lightgray', width=2)
        
        # Convert to PNG bytes
        buffer = io.BytesIO()
        image.save(buffer, format='PNG')
        return buffer.getvalue()
    
    def log_message(self, format, *args):
        """Override to reduce log verbosity."""
        pass  # Comment this out to see all requests

def create_sample_tiles():
    """Create some sample tile files for offline testing."""
    print("Creating sample tiles...")
    
    # Create tiles directory structure
    for zoom in range(8, 12):  # Zoom levels 8-11
        for x in range(2**(zoom-2), 2**(zoom-1)):  # Sample range
            for y in range(2**(zoom-2), 2**(zoom-1)):
                tile_dir = f"tiles/{zoom}/{x}"
                os.makedirs(tile_dir, exist_ok=True)
                
                # Generate tile
                handler = TileHandler()
                tile_data = handler.generate_tile(zoom, x, y)
                
                # Save tile
                tile_path = f"{tile_dir}/{y}.png"
                with open(tile_path, 'wb') as f:
                    f.write(tile_data)
    
    print("Sample tiles created in 'tiles' directory")

def main():
    """Main server function."""
    port = 8080
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "create":
            create_sample_tiles()
            return
        else:
            try:
                port = int(sys.argv[1])
            except ValueError:
                print(f"Invalid port: {sys.argv[1]}")
                return
    
    print(f"Starting TMS tile server on port {port}")
    print(f"Tile URL format: http://localhost:{port}/{{zoom}}/{{x}}/{{y}}.png")
    print("Press Ctrl+C to stop")
    
    try:
        with socketserver.TCPServer(("", port), TileHandler) as httpd:
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\nServer stopped")
    except OSError as e:
        print(f"Error starting server: {e}")
        print(f"Port {port} might already be in use")

if __name__ == "__main__":
    # Check if PIL is available
    try:
        from PIL import Image, ImageDraw, ImageFont
    except ImportError:
        print("PIL (Pillow) is required for tile generation")
        print("Install with: pip install Pillow")
        sys.exit(1)
    
    main()
