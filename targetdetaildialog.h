#ifndef TARGETDETAILDIALOG_H
#define TARGETDETAILDIALOG_H

#include <QDialog>
#include <QLabel>
#include <QLineEdit>
#include <QTextEdit>
#include <QPushButton>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QFormLayout>
#include <QGroupBox>
#include <QJsonObject>
#include "udpmanager.h"

QT_BEGIN_NAMESPACE
namespace Ui { class TargetDetailDialog; }
QT_END_NAMESPACE

/**
 * @brief Dialog for displaying detailed target information
 * 
 * Shows comprehensive target data including:
 * - Basic information (ID, type, name)
 * - Position data (coordinates, altitude)
 * - Additional JSON data
 * - Action buttons for interaction
 */
class TargetDetailDialog : public QDialog
{
    Q_OBJECT

public:
    explicit TargetDetailDialog(const UdpManager::TargetData& targetData, QWidget *parent = nullptr);
    ~TargetDetailDialog();
    
    /**
     * @brief Update dialog with new target data
     * @param targetData Updated target data
     */
    void updateTargetData(const UdpManager::TargetData& targetData);
    
    /**
     * @brief Get the target ID
     */
    QString getTargetId() const;

signals:
    /**
     * @brief Emitted when user wants to track this target
     * @param targetId ID of target to track
     */
    void trackTargetRequested(const QString& targetId);
    
    /**
     * @brief Emitted when user wants to send a response
     * @param targetId ID of target
     * @param responseData Additional response data
     */
    void sendResponseRequested(const QString& targetId, const QJsonObject& responseData);

private slots:
    void onTrackButtonClicked();
    void onSendResponseClicked();
    void onCopyCoordinatesClicked();

private:
    void setupUI();
    void populateFields();
    QString formatCoordinate(double value, bool isLatitude) const;
    QString formatDateTime(const QDateTime& dateTime) const;
    
    Ui::TargetDetailDialog *ui;
    UdpManager::TargetData m_targetData;
    
    // UI Components
    QLabel* m_iconLabel;
    QLineEdit* m_idEdit;
    QLineEdit* m_typeEdit;
    QLineEdit* m_nameEdit;
    QLineEdit* m_latitudeEdit;
    QLineEdit* m_longitudeEdit;
    QLineEdit* m_altitudeEdit;
    QTextEdit* m_descriptionEdit;
    QTextEdit* m_additionalDataEdit;
    QLabel* m_lastUpdateLabel;
    
    QPushButton* m_trackButton;
    QPushButton* m_sendResponseButton;
    QPushButton* m_copyCoordinatesButton;
    QPushButton* m_closeButton;
};

#endif // TARGETDETAILDIALOG_H
