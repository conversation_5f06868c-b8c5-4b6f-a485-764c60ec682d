@echo off
REM Build script for Professional Map Application
REM Requires Qt 5.14 or later with qmake in PATH

echo Building Professional Map Application...
echo.

REM Check if qmake is available
qmake --version >nul 2>&1
if errorlevel 1 (
    echo Error: qmake not found in PATH
    echo Please ensure Qt 5.14 or later is installed and qmake is in your PATH
    pause
    exit /b 1
)

REM Clean previous build
if exist Makefile (
    echo Cleaning previous build...
    nmake clean >nul 2>&1
    mingw32-make clean >nul 2>&1
    del Makefile >nul 2>&1
)

REM Generate Makefile
echo Generating Makefile...
qmake map.pro
if errorlevel 1 (
    echo Error: Failed to generate Makefile
    pause
    exit /b 1
)

REM Build the application
echo Building application...
if exist "C:\Program Files (x86)\Microsoft Visual Studio" (
    echo Using MSVC compiler...
    nmake
) else (
    echo Using MinGW compiler...
    mingw32-make
)

if errorlevel 1 (
    echo Error: Build failed
    pause
    exit /b 1
)

echo.
echo Build completed successfully!
echo.
echo To run the application:
echo   1. Start the tile server: python tile_server.py
echo   2. Run the application: map.exe
echo   3. Test UDP: python test_udp.py
echo.
pause
