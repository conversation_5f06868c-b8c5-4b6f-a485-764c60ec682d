# Professional Offline Map Application

A professional-grade offline map application built with Qt 5.14 and qmake, featuring TMS tile loading, real-time target tracking via UDP, and smooth map interactions.

## Features

### Core Map Functionality
- ✅ Local TMS (Tile Map Service) format tiles from localhost:8080
- ✅ GCJ02 Web Mercator projection support
- ✅ Smooth drag panning and mouse wheel zooming
- ✅ Real-time zoom level and cursor coordinates display

### Dynamic Target System
- ✅ UDP JSON message reception and parsing
- ✅ Dynamic target icon loading (red.png/blue.png based on targettype)
- ✅ Real-time target position updates
- ✅ Target click interaction with UDP response
- ✅ Detailed target information dialog

### Performance Features
- ✅ Multi-level caching (memory + disk)
- ✅ Asynchronous multi-threaded tile loading
- ✅ Viewport-aware tile loading
- ✅ Smooth zoom transitions without flickering
- ✅ Optimized rendering with 60 FPS target
- ✅ Robust UDP communication with error handling

## Project Structure

```
map/
├── main.cpp                    # Application entry point
├── mainwindow.h/.cpp/.ui       # Main application window
├── mapwidget.h/.cpp            # Core map widget with rendering
├── tilemanager.h/.cpp          # Tile loading and caching
├── tilecache.h/.cpp            # Multi-level cache system
├── udpmanager.h/.cpp           # UDP communication
├── target.h/.cpp               # Target representation and interaction
├── targetdetaildialog.h/.cpp/.ui # Target detail dialog
├── coordinateconverter.h/.cpp  # GCJ02 Web Mercator utilities
├── resources.qrc               # Resource file for icons
├── icons/                      # Target and UI icons
└── map.pro                     # qmake project file
```

## Building

### Prerequisites
- Qt 5.14 or later
- qmake (included with Qt)
- C++11 compatible compiler (GCC, Clang, or MSVC)

### Build Steps

#### Option 1: Using Build Scripts
```bash
# Linux/Mac
chmod +x build.sh
./build.sh

# Windows
build.bat
```

#### Option 2: Manual Build
```bash
# Generate Makefile
qmake map.pro

# Build the application
make
# or on Windows with MSVC:
nmake
# or with MinGW:
mingw32-make
```

### Qt Installation Notes
- Ensure Qt 5.14+ is installed with development tools
- Add Qt's bin directory to your PATH (contains qmake)
- Example Qt bin paths:
  - Windows: `C:\Qt\5.14.2\msvc2017_64\bin`
  - Linux: `/opt/Qt/5.14.2/gcc_64/bin`
  - macOS: `/Users/<USER>/Qt/5.14.2/clang_64/bin`

### Running
```bash
# Start the application
./map
# or on Windows:
map.exe
```

## Configuration

### TMS Tile Server
The application expects a TMS tile server running on `localhost:8080` with the format:
```
http://localhost:8080/{zoom}/{x}/{y}.png
```

### UDP Communication
- **Listen Port**: 12345 (configurable in mainwindow.h)
- **Response Port**: 12346 (default destination)

### Target JSON Format
```json
{
  "type": "target",
  "action": "add",
  "id": "target_001",
  "targettype": "enemy",
  "latitude": 39.9093,
  "longitude": 116.3974,
  "altitude": 100.0,
  "name": "Target Name",
  "description": "Target description"
}
```

### Target Types and Icons
- **Red Icon**: enemy, threat, hostile, danger, alert, or contains "red"
- **Blue Icon**: friendly, ally, safe, neutral, info, or contains "blue"
- **Default**: blue.png

## Usage

### Map Navigation
- **Pan**: Click and drag with left mouse button
- **Zoom**: Mouse wheel up/down
- **Coordinates**: Displayed in bottom-left corner
- **Zoom Level**: Displayed in top-left corner

### Target Interaction
- **Click Target**: Opens detail dialog and sends UDP response
- **Target Dialog**: Shows comprehensive target information
- **Track Target**: Centers map on target location
- **Send Response**: Sends custom UDP response message

### Status Bar
- **Coordinates**: Current mouse position (lat/lon)
- **Target Count**: Number of active targets
- **Zoom Level**: Current map zoom level
- **UDP Status**: Connection status and port

## Performance Optimizations

### Caching
- **Memory Cache**: 100 MB default (configurable)
- **Disk Cache**: 1 GB default (configurable)
- **LRU Eviction**: Automatic cleanup of old tiles

### Rendering
- **60 FPS Target**: Smooth animation and interaction
- **Viewport Culling**: Only render visible tiles and targets
- **Async Loading**: Non-blocking tile requests
- **Smooth Transitions**: Animated zoom and pan operations

### Memory Management
- **Smart Pointers**: Automatic cleanup of resources
- **Object Pooling**: Efficient target management
- **Lazy Loading**: Load resources only when needed

## Error Handling

### Network Errors
- **Connection Timeouts**: Automatic retry with exponential backoff
- **Invalid Responses**: Graceful degradation with error logging
- **Server Unavailable**: Cached tile fallback

### UDP Communication
- **Parse Errors**: Invalid JSON handling with error reporting
- **Connection Loss**: Automatic reconnection attempts
- **Message Validation**: Schema validation for target data

### Resource Management
- **Missing Icons**: Automatic fallback to generated icons
- **Memory Limits**: Automatic cache eviction
- **Disk Space**: Cache size monitoring and cleanup

## Testing

### Manual Testing
1. Start a TMS tile server on localhost:8080
2. Run the application
3. Test map navigation (pan, zoom)
4. Send UDP target messages to port 12345
5. Click targets to test interaction
6. Verify coordinate display and status updates

### UDP Test Messages
```bash
# Add target
echo '{"type":"target","action":"add","id":"test1","targettype":"enemy","latitude":39.9093,"longitude":116.3974,"name":"Test Target"}' | nc -u localhost 12345

# Update target
echo '{"type":"target","action":"update","id":"test1","latitude":39.9100,"longitude":116.3980}' | nc -u localhost 12345

# Remove target
echo '{"type":"target","action":"remove","id":"test1"}' | nc -u localhost 12345
```

## Troubleshooting

### Build Issues
1. **qmake not found**:
   - Install Qt 5.14+ with development tools
   - Add Qt's bin directory to PATH
   - Verify with: `qmake --version`

2. **Compilation errors**:
   - Ensure C++11 support is enabled
   - Check Qt modules are properly installed
   - Verify all source files are present

3. **Resource compilation errors**:
   - Run `python create_simple_pngs.py` to create icon files
   - Ensure icons directory exists with PNG files

### Runtime Issues
1. **No tiles loading**: Check TMS server on localhost:8080
2. **UDP not working**: Verify port 12345 is available
3. **Missing icons**: Icons will be generated as colored squares if PNG files are missing
4. **Performance issues**: Adjust cache sizes in tilecache.h

### Debug Output
Enable debug output by setting QT_LOGGING_RULES:
```bash
# Linux/Mac
export QT_LOGGING_RULES="*.debug=true"
./map

# Windows
set QT_LOGGING_RULES=*.debug=true
map.exe
```

### Fixed Compilation Issues
The following issues have been resolved:
- ✅ Missing `scheduleRepaint()` declaration in MapWidget
- ✅ Missing `QDateTime` include in UdpManager and TargetDetailDialog
- ✅ `positionChanged` signal conflict in Target class
- ✅ `setTransferTimeout` not available in Qt 5.14 (commented out)
- ✅ Mutex const-correctness in TileManager
- ✅ Missing PNG icon files (created minimal placeholders)

## License

This is a professional implementation for demonstration purposes.
