#include "tilecache.h"
#include <QMutexLocker>
#include <QPixmap>
#include <QBuffer>
#include <QImageWriter>
#include <QImageReader>
#include <QDebug>
#include <QDirIterator>

TileCache::TileCache(QObject *parent)
    : QObject(parent)
    , m_memoryCacheSize(0)
    , m_maxMemoryCacheSize(DEFAULT_MEMORY_CACHE_SIZE)
    , m_diskCacheSize(0)
    , m_maxDiskCacheSize(DEFAULT_DISK_CACHE_SIZE)
    , m_cleanupTimer(new QTimer(this))
{
    // Setup cache directory
    ensureCacheDirectory();
    
    // Calculate initial disk cache size
    m_diskCacheSize = calculateDiskCacheSize();
    
    // Setup cleanup timer (run every 5 minutes)
    m_cleanupTimer->setInterval(5 * 60 * 1000);
    connect(m_cleanupTimer, &QTimer::timeout, this, &TileCache::cleanupCache);
    m_cleanupTimer->start();
}

TileCache::~TileCache()
{
    clear();
}

QPixmap TileCache::getTile(const TileKey& key)
{
    // First check memory cache
    {
        QMutexLocker locker(&m_memoryCacheMutex);
        auto it = m_memoryCache.find(key);
        if (it != m_memoryCache.end()) {
            // Update access time
            it->lastAccessed = QDateTime::currentDateTime();
            return it->pixmap;
        }
    }
    
    // Check disk cache
    {
        QMutexLocker locker(&m_diskCacheMutex);
        QString filePath = getTilePath(key);
        if (QFile::exists(filePath)) {
            QPixmap pixmap(filePath);
            if (!pixmap.isNull()) {
                // Load into memory cache
                putTile(key, pixmap);
                return pixmap;
            }
        }
    }
    
    return QPixmap(); // Not found
}

void TileCache::putTile(const TileKey& key, const QPixmap& pixmap)
{
    if (pixmap.isNull()) {
        return;
    }
    
    // Calculate pixmap size
    qint64 pixmapSize = pixmap.width() * pixmap.height() * pixmap.depth() / 8;
    
    // Store in memory cache
    {
        QMutexLocker locker(&m_memoryCacheMutex);
        
        // Remove existing entry if present
        auto it = m_memoryCache.find(key);
        if (it != m_memoryCache.end()) {
            m_memoryCacheSize -= it->size;
            m_memoryCache.erase(it);
        }
        
        // Add new entry
        CacheEntry entry;
        entry.pixmap = pixmap;
        entry.lastAccessed = QDateTime::currentDateTime();
        entry.size = pixmapSize;
        
        m_memoryCache.insert(key, entry);
        m_memoryCacheSize += pixmapSize;
        
        // Evict if necessary
        if (m_memoryCacheSize > m_maxMemoryCacheSize) {
            evictMemoryCache();
        }
    }
    
    // Store in disk cache
    {
        QMutexLocker locker(&m_diskCacheMutex);
        QString filePath = getTilePath(key);
        
        // Ensure directory exists
        QDir dir = QFileInfo(filePath).dir();
        if (!dir.exists()) {
            dir.mkpath(".");
        }
        
        // Save pixmap
        if (pixmap.save(filePath, "PNG")) {
            QFileInfo fileInfo(filePath);
            m_diskCacheSize += fileInfo.size();
            
            // Evict if necessary
            if (m_diskCacheSize > m_maxDiskCacheSize) {
                evictDiskCache();
            }
        }
    }
}

bool TileCache::hasTile(const TileKey& key)
{
    // Check memory cache first
    {
        QMutexLocker locker(&m_memoryCacheMutex);
        if (m_memoryCache.contains(key)) {
            return true;
        }
    }
    
    // Check disk cache
    {
        QMutexLocker locker(&m_diskCacheMutex);
        return QFile::exists(getTilePath(key));
    }
}

void TileCache::clear()
{
    // Clear memory cache
    {
        QMutexLocker locker(&m_memoryCacheMutex);
        m_memoryCache.clear();
        m_memoryCacheSize = 0;
    }
    
    // Clear disk cache
    {
        QMutexLocker locker(&m_diskCacheMutex);
        if (m_cacheDir.exists()) {
            m_cacheDir.removeRecursively();
            ensureCacheDirectory();
        }
        m_diskCacheSize = 0;
    }
}

qint64 TileCache::getMemoryCacheSize() const
{
    QMutexLocker locker(&m_memoryCacheMutex);
    return m_memoryCacheSize;
}

qint64 TileCache::getDiskCacheSize() const
{
    QMutexLocker locker(&m_diskCacheMutex);
    return m_diskCacheSize;
}

void TileCache::setMaxMemoryCacheSize(qint64 maxSize)
{
    QMutexLocker locker(&m_memoryCacheMutex);
    m_maxMemoryCacheSize = maxSize;
    if (m_memoryCacheSize > m_maxMemoryCacheSize) {
        evictMemoryCache();
    }
}

void TileCache::setMaxDiskCacheSize(qint64 maxSize)
{
    QMutexLocker locker(&m_diskCacheMutex);
    m_maxDiskCacheSize = maxSize;
    if (m_diskCacheSize > m_maxDiskCacheSize) {
        evictDiskCache();
    }
}

void TileCache::cleanupCache()
{
    // Clean up old entries from memory cache
    {
        QMutexLocker locker(&m_memoryCacheMutex);
        QDateTime cutoff = QDateTime::currentDateTime().addSecs(-3600); // 1 hour ago
        
        auto it = m_memoryCache.begin();
        while (it != m_memoryCache.end()) {
            if (it->lastAccessed < cutoff) {
                m_memoryCacheSize -= it->size;
                it = m_memoryCache.erase(it);
            } else {
                ++it;
            }
        }
    }
    
    // Recalculate disk cache size
    {
        QMutexLocker locker(&m_diskCacheMutex);
        m_diskCacheSize = calculateDiskCacheSize();
    }
}

QString TileCache::getTilePath(const TileKey& key) const
{
    return m_cacheDir.absoluteFilePath(getTileFileName(key));
}

QString TileCache::getTileFileName(const TileKey& key) const
{
    return QString("%1/%2/%3.png").arg(key.zoom).arg(key.x).arg(key.y);
}

void TileCache::ensureCacheDirectory()
{
    QString cacheLocation = QStandardPaths::writableLocation(QStandardPaths::CacheLocation);
    m_cacheDir = QDir(cacheLocation + "/map_tiles");
    
    if (!m_cacheDir.exists()) {
        m_cacheDir.mkpath(".");
    }
}

void TileCache::evictMemoryCache()
{
    // Remove oldest entries until under limit
    while (m_memoryCacheSize > m_maxMemoryCacheSize && !m_memoryCache.isEmpty()) {
        auto oldestIt = m_memoryCache.begin();
        QDateTime oldestTime = oldestIt->lastAccessed;
        
        for (auto it = m_memoryCache.begin(); it != m_memoryCache.end(); ++it) {
            if (it->lastAccessed < oldestTime) {
                oldestTime = it->lastAccessed;
                oldestIt = it;
            }
        }
        
        m_memoryCacheSize -= oldestIt->size;
        m_memoryCache.erase(oldestIt);
    }
}

void TileCache::evictDiskCache()
{
    // Simple strategy: remove files until under limit
    // In a production system, you might want a more sophisticated LRU strategy
    QDirIterator it(m_cacheDir.absolutePath(), QStringList() << "*.png", 
                    QDir::Files, QDirIterator::Subdirectories);
    
    QList<QPair<QDateTime, QString>> files;
    while (it.hasNext()) {
        QString filePath = it.next();
        QFileInfo fileInfo(filePath);
        files.append(qMakePair(fileInfo.lastModified(), filePath));
    }
    
    // Sort by modification time (oldest first)
    std::sort(files.begin(), files.end());
    
    // Remove oldest files until under limit
    for (const auto& file : files) {
        if (m_diskCacheSize <= m_maxDiskCacheSize) {
            break;
        }
        
        QFileInfo fileInfo(file.second);
        qint64 fileSize = fileInfo.size();
        
        if (QFile::remove(file.second)) {
            m_diskCacheSize -= fileSize;
        }
    }
}

qint64 TileCache::calculateDiskCacheSize()
{
    qint64 totalSize = 0;
    QDirIterator it(m_cacheDir.absolutePath(), QStringList() << "*.png", 
                    QDir::Files, QDirIterator::Subdirectories);
    
    while (it.hasNext()) {
        it.next();
        QFileInfo fileInfo(it.fileInfo());
        totalSize += fileInfo.size();
    }
    
    return totalSize;
}
