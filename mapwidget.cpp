#include "mapwidget.h"
#include <QPainter>
#include <QMouseEvent>
#include <QWheelEvent>
#include <QApplication>
#include <QDebug>
#include <QEasingCurve>
#include <cmath>

MapWidget::MapWidget(QWidget *parent)
    : QWidget(parent)
    , m_center(116.3974, 39.9093) // Beijing coordinates as default
    , m_zoomLevel(10)
    , m_dragging(false)
    , m_tileManager(new TileManager(this))
    , m_coordinateLabel(nullptr)
    , m_zoomLabel(nullptr)
    , m_centerAnimation(nullptr)
    , m_zoomAnimation(nullptr)
    , m_repaintTimer(new QTimer(this))
    , m_needsRepaint(true)
{
    setupUI();
    
    // Connect tile manager signals
    connect(m_tileManager, &TileManager::tileLoaded,
            this, &MapWidget::onTileLoaded);
    connect(m_tileManager, &TileManager::tileLoadFailed,
            this, &MapWidget::onTileLoadFailed);
    
    // Setup repaint timer for smooth rendering
    m_repaintTimer->setInterval(REPAINT_INTERVAL);
    m_repaintTimer->setSingleShot(true);
    connect(m_repaintTimer, &QTimer::timeout, [this]() {
        if (m_needsRepaint) {
            update();
            m_needsRepaint = false;
        }
    });
    
    // Setup animations
    m_centerAnimation = new QPropertyAnimation(this, "center", this);
    m_centerAnimation->setDuration(ANIMATION_DURATION);
    m_centerAnimation->setEasingCurve(QEasingCurve::OutCubic);
    connect(m_centerAnimation, &QPropertyAnimation::finished,
            this, &MapWidget::onCenterAnimationFinished);
    
    m_zoomAnimation = new QPropertyAnimation(this, "zoomLevel", this);
    m_zoomAnimation->setDuration(ANIMATION_DURATION);
    m_zoomAnimation->setEasingCurve(QEasingCurve::OutCubic);
    connect(m_zoomAnimation, &QPropertyAnimation::finished,
            this, &MapWidget::onZoomAnimationFinished);
    
    // Enable mouse tracking for coordinate display
    setMouseTracking(true);
    
    // Set focus policy for keyboard events
    setFocusPolicy(Qt::StrongFocus);
    
    // Request initial tiles
    QTimer::singleShot(100, this, &MapWidget::requestVisibleTiles);
}

MapWidget::~MapWidget()
{
    clearTargets();
}

void MapWidget::setCenter(const QPointF& center, bool animated)
{
    QPointF newCenter = center;
    newCenter.setX(CoordinateConverter::clampLongitude(newCenter.x()));
    newCenter.setY(CoordinateConverter::clampLatitude(newCenter.y()));
    
    if (m_center == newCenter) {
        return;
    }
    
    if (animated && m_centerAnimation) {
        startCenterAnimation(newCenter);
    } else {
        m_center = newCenter;
        emit centerChanged();
        updateTargetPositions();
        requestVisibleTiles();
        scheduleRepaint();
    }
}

void MapWidget::setZoomLevel(int zoom, bool animated)
{
    zoom = qBound(MIN_ZOOM_LEVEL, zoom, MAX_ZOOM_LEVEL);
    
    if (m_zoomLevel == zoom) {
        return;
    }
    
    if (animated && m_zoomAnimation) {
        startZoomAnimation(zoom);
    } else {
        m_zoomLevel = zoom;
        emit zoomLevelChanged();
        updateTargetPositions();
        requestVisibleTiles();
        scheduleRepaint();
    }
}

void MapWidget::zoomToBounds(const QRectF& bounds)
{
    // Calculate center
    QPointF center = bounds.center();
    
    // Calculate zoom level to fit bounds
    double latRange = bounds.height();
    double lonRange = bounds.width();
    
    // Estimate zoom level based on widget size and coordinate range
    double widgetAspect = static_cast<double>(width()) / height();
    double boundsAspect = lonRange / latRange;
    
    double scale;
    if (boundsAspect > widgetAspect) {
        // Limited by width
        scale = width() / (lonRange * 111320.0); // Approximate meters per degree
    } else {
        // Limited by height
        scale = height() / (latRange * 111320.0);
    }
    
    int targetZoom = qBound(MIN_ZOOM_LEVEL, 
                           static_cast<int>(std::log2(scale * TILE_SIZE)), 
                           MAX_ZOOM_LEVEL);
    
    // Set center and zoom
    setCenter(center, true);
    setZoomLevel(targetZoom, true);
}

void MapWidget::addTarget(Target* target)
{
    if (!target) {
        return;
    }
    
    QString id = target->getId();
    if (m_targets.contains(id)) {
        // Remove existing target
        removeTarget(id);
    }
    
    m_targets.insert(id, target);
    
    // Connect target signals
    connect(target, &Target::needsRepaint, this, &MapWidget::onTargetNeedsRepaint);
    connect(target, &Target::clicked, this, &MapWidget::targetClicked);
    
    // Update target screen position
    QPointF latLon = target->position();
    QPointF screenPos = latLonToScreen(latLon);
    target->setScreenPosition(screenPos);
    
    scheduleRepaint();
}

void MapWidget::removeTarget(const QString& targetId)
{
    auto it = m_targets.find(targetId);
    if (it != m_targets.end()) {
        Target* target = it.value();
        m_targets.erase(it);
        target->deleteLater();
        scheduleRepaint();
    }
}

Target* MapWidget::getTarget(const QString& targetId)
{
    return m_targets.value(targetId, nullptr);
}

void MapWidget::clearTargets()
{
    for (Target* target : m_targets) {
        target->deleteLater();
    }
    m_targets.clear();
    scheduleRepaint();
}

QPointF MapWidget::screenToLatLon(const QPointF& screenPos) const
{
    // Convert screen coordinates to tile coordinates
    QPointF centerTile = CoordinateConverter::latLonToTile(m_center.y(), m_center.x(), m_zoomLevel);
    
    // Calculate offset from center
    QPointF centerScreen(width() / 2.0, height() / 2.0);
    QPointF offset = (screenPos - centerScreen) / TILE_SIZE;
    
    // Add offset to center tile
    QPointF targetTile = centerTile + offset;
    
    // Convert back to lat/lon
    QPointF latLon = CoordinateConverter::tileToLatLon(targetTile.x(), targetTile.y(), m_zoomLevel);
    return QPointF(latLon.x(), latLon.y()); // lon, lat
}

QPointF MapWidget::latLonToScreen(const QPointF& latLon) const
{
    // Convert lat/lon to tile coordinates
    QPointF targetTile = CoordinateConverter::latLonToTile(latLon.y(), latLon.x(), m_zoomLevel);
    QPointF centerTile = CoordinateConverter::latLonToTile(m_center.y(), m_center.x(), m_zoomLevel);
    
    // Calculate offset in tile coordinates
    QPointF tileOffset = targetTile - centerTile;
    
    // Convert to screen coordinates
    QPointF centerScreen(width() / 2.0, height() / 2.0);
    QPointF screenPos = centerScreen + tileOffset * TILE_SIZE;
    
    return screenPos;
}

void MapWidget::paintEvent(QPaintEvent* event)
{
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing, true);
    painter.setRenderHint(QPainter::SmoothPixmapTransform, true);

    // Fill background
    painter.fillRect(rect(), QColor(240, 240, 240));

    // Draw tiles
    drawTiles(painter);

    // Draw targets
    drawTargets(painter);

    // Draw UI overlays
    drawCoordinateDisplay(painter);
    drawZoomLevel(painter);
}

void MapWidget::mousePressEvent(QMouseEvent* event)
{
    if (event->button() == Qt::LeftButton) {
        // Check if clicking on a target
        Target* target = getTargetAtPosition(event->pos());
        if (target) {
            emit targetClicked(target);
            return;
        }

        // Start dragging
        m_dragging = true;
        m_lastPanPoint = event->pos();
        m_dragStartPoint = event->pos();
        setCursor(Qt::ClosedHandCursor);
    }

    QWidget::mousePressEvent(event);
}

void MapWidget::mouseMoveEvent(QMouseEvent* event)
{
    // Update mouse coordinates
    updateMouseCoordinates(event->pos());

    if (m_dragging) {
        // Calculate pan delta
        QPoint delta = event->pos() - m_lastPanPoint;
        m_lastPanPoint = event->pos();

        // Convert delta to coordinate offset
        QPointF currentCenter = screenToLatLon(QPointF(width() / 2.0, height() / 2.0));
        QPointF newCenter = screenToLatLon(QPointF(width() / 2.0 - delta.x(), height() / 2.0 - delta.y()));

        setCenter(newCenter, false);
    }

    QWidget::mouseMoveEvent(event);
}

void MapWidget::mouseReleaseEvent(QMouseEvent* event)
{
    if (event->button() == Qt::LeftButton && m_dragging) {
        m_dragging = false;
        setCursor(Qt::ArrowCursor);

        // Check if this was a click (small movement)
        QPoint totalDelta = event->pos() - m_dragStartPoint;
        if (totalDelta.manhattanLength() < 5) {
            // This was a click, not a drag
            // Handle map click if needed
        }
    }

    QWidget::mouseReleaseEvent(event);
}

void MapWidget::wheelEvent(QWheelEvent* event)
{
    // Zoom in/out based on wheel direction
    int delta = event->angleDelta().y();
    if (delta > 0) {
        setZoomLevel(m_zoomLevel + 1, true);
    } else if (delta < 0) {
        setZoomLevel(m_zoomLevel - 1, true);
    }

    QWidget::wheelEvent(event);
}

void MapWidget::resizeEvent(QResizeEvent* event)
{
    QWidget::resizeEvent(event);

    // Update target positions
    updateTargetPositions();

    // Request new tiles for the new viewport
    requestVisibleTiles();

    scheduleRepaint();
}

void MapWidget::leaveEvent(QEvent* event)
{
    // Clear mouse coordinates when mouse leaves widget
    m_mouseCoordinates = QPointF();
    emit mouseCoordinatesChanged(m_mouseCoordinates);

    QWidget::leaveEvent(event);
}

void MapWidget::onTileLoaded(int zoom, int x, int y, const QPixmap& pixmap)
{
    // Only repaint if this tile is for the current zoom level
    if (zoom == m_zoomLevel) {
        scheduleRepaint();
    }
}

void MapWidget::onTileLoadFailed(int zoom, int x, int y, const QString& error)
{
    qWarning() << "Tile load failed:" << zoom << x << y << error;
}

void MapWidget::onTargetNeedsRepaint()
{
    scheduleRepaint();
}

void MapWidget::onCenterAnimationFinished()
{
    requestVisibleTiles();
    scheduleRepaint();
}

void MapWidget::onZoomAnimationFinished()
{
    updateTargetPositions();
    requestVisibleTiles();
    scheduleRepaint();
}

void MapWidget::updateTargetPositions()
{
    for (Target* target : m_targets) {
        QPointF latLon = target->position();
        QPointF screenPos = latLonToScreen(latLon);
        target->setScreenPosition(screenPos);
    }
}

void MapWidget::setupUI()
{
    // Create coordinate display label
    m_coordinateLabel = new QLabel(this);
    m_coordinateLabel->setStyleSheet(
        "QLabel { "
        "background-color: rgba(0, 0, 0, 180); "
        "color: white; "
        "padding: 5px; "
        "border-radius: 3px; "
        "font-family: monospace; "
        "}"
    );
    m_coordinateLabel->move(10, height() - 30);

    // Create zoom level display label
    m_zoomLabel = new QLabel(this);
    m_zoomLabel->setStyleSheet(
        "QLabel { "
        "background-color: rgba(0, 0, 0, 180); "
        "color: white; "
        "padding: 5px; "
        "border-radius: 3px; "
        "font-family: monospace; "
        "}"
    );
    m_zoomLabel->move(10, 10);
    m_zoomLabel->setText(QString("Zoom: %1").arg(m_zoomLevel));
}

void MapWidget::scheduleRepaint()
{
    m_needsRepaint = true;
    if (!m_repaintTimer->isActive()) {
        m_repaintTimer->start();
    }
}

void MapWidget::requestVisibleTiles()
{
    QRect tileRect = getVisibleTileRect();
    m_tileManager->requestTilesForViewport(tileRect, m_zoomLevel);
}

void MapWidget::drawTiles(QPainter& painter)
{
    QRect tileRect = getVisibleTileRect();
    QPointF tileOffset = getTileOffset();

    for (int x = tileRect.left(); x <= tileRect.right(); ++x) {
        for (int y = tileRect.top(); y <= tileRect.bottom(); ++y) {
            QPixmap tile = m_tileManager->getTile(m_zoomLevel, x, y);

            if (!tile.isNull()) {
                // Calculate tile position on screen
                QPointF tilePos = QPointF(x - tileOffset.x(), y - tileOffset.y()) * TILE_SIZE;
                painter.drawPixmap(tilePos.toPoint(), tile);
            }
        }
    }
}

void MapWidget::drawTargets(QPainter& painter)
{
    for (Target* target : m_targets) {
        if (!target->isVisible()) {
            continue;
        }

        QPointF screenPos = target->getScreenPosition();
        QPixmap icon = target->getIcon();

        if (!icon.isNull()) {
            // Apply opacity
            painter.setOpacity(target->opacity());

            // Draw icon centered on position
            QPoint iconPos = screenPos.toPoint() - QPoint(icon.width() / 2, icon.height() / 2);
            painter.drawPixmap(iconPos, icon);

            // Reset opacity
            painter.setOpacity(1.0);

            // Draw selection indicator if selected
            if (target->getState() == Target::Selected || target->getState() == Target::Highlighted) {
                painter.setPen(QPen(Qt::yellow, 2));
                painter.setBrush(Qt::NoBrush);
                QRect selectionRect = target->getBoundingRect();
                selectionRect.adjust(-2, -2, 2, 2);
                painter.drawRect(selectionRect);
            }
        }
    }
}

void MapWidget::drawCoordinateDisplay(QPainter& painter)
{
    if (!m_mouseCoordinates.isNull()) {
        QString coordText = QString("Lat: %1°, Lon: %2°")
                           .arg(m_mouseCoordinates.y(), 0, 'f', 6)
                           .arg(m_mouseCoordinates.x(), 0, 'f', 6);

        m_coordinateLabel->setText(coordText);
        m_coordinateLabel->adjustSize();
        m_coordinateLabel->move(10, height() - m_coordinateLabel->height() - 10);
    }
}

void MapWidget::drawZoomLevel(QPainter& painter)
{
    m_zoomLabel->setText(QString("Zoom: %1").arg(m_zoomLevel));
    m_zoomLabel->adjustSize();
}

QRect MapWidget::getVisibleTileRect() const
{
    // Get center tile
    QPointF centerTile = CoordinateConverter::latLonToTile(m_center.y(), m_center.x(), m_zoomLevel);

    // Calculate how many tiles we need in each direction
    int tilesX = (width() / TILE_SIZE) + 2;  // +2 for partial tiles
    int tilesY = (height() / TILE_SIZE) + 2;

    // Calculate tile bounds
    int minX = static_cast<int>(std::floor(centerTile.x() - tilesX / 2.0));
    int maxX = static_cast<int>(std::ceil(centerTile.x() + tilesX / 2.0));
    int minY = static_cast<int>(std::floor(centerTile.y() - tilesY / 2.0));
    int maxY = static_cast<int>(std::ceil(centerTile.y() + tilesY / 2.0));

    // Clamp to valid tile range
    int maxTileIndex = (1 << m_zoomLevel) - 1;
    minX = qMax(0, minX);
    maxX = qMin(maxTileIndex, maxX);
    minY = qMax(0, minY);
    maxY = qMin(maxTileIndex, maxY);

    return QRect(minX, minY, maxX - minX, maxY - minY);
}

QPointF MapWidget::getTileOffset() const
{
    QPointF centerTile = CoordinateConverter::latLonToTile(m_center.y(), m_center.x(), m_zoomLevel);
    QPointF centerScreen(width() / 2.0, height() / 2.0);

    // Calculate the offset of the center tile from the screen center
    QPointF tileScreenPos = centerScreen / TILE_SIZE;
    return centerTile - tileScreenPos;
}

Target* MapWidget::getTargetAtPosition(const QPointF& pos) const
{
    // Check targets in reverse order (top-most first)
    QList<Target*> targets = m_targets.values();
    for (int i = targets.size() - 1; i >= 0; --i) {
        Target* target = targets[i];
        if (target->contains(pos)) {
            return target;
        }
    }
    return nullptr;
}

void MapWidget::updateMouseCoordinates(const QPointF& screenPos)
{
    QPointF latLon = screenToLatLon(screenPos);
    if (m_mouseCoordinates != latLon) {
        m_mouseCoordinates = latLon;
        emit mouseCoordinatesChanged(latLon);
    }
}

void MapWidget::startCenterAnimation(const QPointF& targetCenter)
{
    if (m_centerAnimation->state() == QAbstractAnimation::Running) {
        m_centerAnimation->stop();
    }

    m_centerAnimation->setStartValue(m_center);
    m_centerAnimation->setEndValue(targetCenter);
    m_centerAnimation->start();
}

void MapWidget::startZoomAnimation(int targetZoom)
{
    if (m_zoomAnimation->state() == QAbstractAnimation::Running) {
        m_zoomAnimation->stop();
    }

    m_zoomAnimation->setStartValue(m_zoomLevel);
    m_zoomAnimation->setEndValue(targetZoom);
    m_zoomAnimation->start();
}
