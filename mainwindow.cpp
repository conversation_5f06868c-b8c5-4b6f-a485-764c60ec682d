#include "mainwindow.h"
#include "ui_mainwindow.h"
#include <QMessageBox>
#include <QApplication>
#include <QDebug>

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , ui(new Ui::MainWindow)
    , m_mapWidget(nullptr)
    , m_udpManager(nullptr)
    , m_coordinateStatusLabel(nullptr)
    , m_connectionStatusLabel(nullptr)
    , m_targetCountLabel(nullptr)
    , m_zoomStatusLabel(nullptr)
    , m_statusUpdateTimer(new QTimer(this))
{
    ui->setupUi(this);
    setupUI();
    setupStatusBar();
    setupConnections();

    // Start UDP manager
    m_udpManager = new UdpManager(this);
    if (!m_udpManager->startListening(UDP_LISTEN_PORT)) {
        QMessageBox::warning(this, "UDP Error",
                           QString("Failed to start UDP listener on port %1").arg(UDP_LISTEN_PORT));
    }

    // Connect UDP signals
    connect(m_udpManager, &UdpManager::targetDataReceived,
            this, &MainWindow::onTargetDataReceived);
    connect(m_udpManager, &UdpManager::targetUpdateReceived,
            this, &MainWindow::onTargetUpdateReceived);
    connect(m_udpManager, &UdpManager::targetRemovalRequested,
            this, &MainWindow::onTargetRemovalRequested);
    connect(m_udpManager, &UdpManager::udpError,
            this, &MainWindow::onUdpError);
    connect(m_udpManager, &UdpManager::connectionStatusChanged,
            this, &MainWindow::onConnectionStatusChanged);

    // Setup status update timer
    m_statusUpdateTimer->setInterval(STATUS_UPDATE_INTERVAL);
    connect(m_statusUpdateTimer, &QTimer::timeout, this, &MainWindow::updateStatusBar);
    m_statusUpdateTimer->start();

    updateWindowTitle();
}

MainWindow::~MainWindow()
{
    // Clean up target dialogs
    for (auto dialog : m_targetDialogs) {
        dialog->close();
        dialog->deleteLater();
    }
    m_targetDialogs.clear();

    // Stop UDP manager
    if (m_udpManager) {
        m_udpManager->stopListening();
    }

    delete ui;
}

void MainWindow::setupUI()
{
    // Create and set up the map widget
    m_mapWidget = new MapWidget(this);
    setCentralWidget(m_mapWidget);

    // Set window properties
    setWindowTitle("Professional Map Application");
    setMinimumSize(800, 600);
    resize(1200, 800);
}

void MainWindow::setupStatusBar()
{
    // Create status bar labels
    m_coordinateStatusLabel = new QLabel("Coordinates: --");
    m_connectionStatusLabel = new QLabel("UDP: Disconnected");
    m_targetCountLabel = new QLabel("Targets: 0");
    m_zoomStatusLabel = new QLabel("Zoom: --");

    // Add labels to status bar
    statusBar()->addWidget(m_coordinateStatusLabel);
    statusBar()->addPermanentWidget(m_targetCountLabel);
    statusBar()->addPermanentWidget(m_zoomStatusLabel);
    statusBar()->addPermanentWidget(m_connectionStatusLabel);

    // Style the status bar
    statusBar()->setStyleSheet(
        "QStatusBar { "
        "border-top: 1px solid gray; "
        "background-color: #f0f0f0; "
        "}"
        "QLabel { "
        "margin: 2px 5px; "
        "}"
    );
}

void MainWindow::setupConnections()
{
    // Connect map widget signals
    connect(m_mapWidget, &MapWidget::targetClicked,
            this, &MainWindow::onTargetClicked);
    connect(m_mapWidget, &MapWidget::mouseCoordinatesChanged,
            this, &MainWindow::onMouseCoordinatesChanged);
    connect(m_mapWidget, &MapWidget::zoomLevelChanged, [this]() {
        m_zoomStatusLabel->setText(QString("Zoom: %1").arg(m_mapWidget->zoomLevel()));
    });

    // Connect menu actions
    connect(ui->actionExit, &QAction::triggered, this, &QWidget::close);
    connect(ui->actionZoomIn, &QAction::triggered, [this]() {
        if (m_mapWidget) {
            m_mapWidget->setZoomLevel(m_mapWidget->zoomLevel() + 1, true);
        }
    });
    connect(ui->actionZoomOut, &QAction::triggered, [this]() {
        if (m_mapWidget) {
            m_mapWidget->setZoomLevel(m_mapWidget->zoomLevel() - 1, true);
        }
    });
    connect(ui->actionZoomToFit, &QAction::triggered, [this]() {
        if (m_mapWidget) {
            // Zoom to Beijing area as default
            QRectF bounds(116.3, 39.85, 0.2, 0.2);
            m_mapWidget->zoomToBounds(bounds);
        }
    });
    connect(ui->actionClearTargets, &QAction::triggered, [this]() {
        if (m_mapWidget) {
            m_mapWidget->clearTargets();
            // Close all target dialogs
            for (auto dialog : m_targetDialogs) {
                dialog->close();
                dialog->deleteLater();
            }
            m_targetDialogs.clear();
        }
    });
    connect(ui->actionAbout, &QAction::triggered, [this]() {
        QMessageBox::about(this, "About",
            "Professional Map Application\n\n"
            "Features:\n"
            "• TMS tile loading from localhost:8080\n"
            "• Real-time target tracking via UDP\n"
            "• Smooth map interactions\n"
            "• Multi-level caching system\n"
            "• Professional-grade performance\n\n"
            "Built with Qt 5.14 and qmake");
    });
}

void MainWindow::onTargetDataReceived(const UdpManager::TargetData& targetData)
{
    qDebug() << "Received target data:" << targetData.id << targetData.targetType;

    // Create new target
    Target* target = new Target(targetData, this);

    // Add to map
    m_mapWidget->addTarget(target);

    // Update status
    updateStatusBar();
}

void MainWindow::onTargetUpdateReceived(const UdpManager::TargetData& targetData)
{
    qDebug() << "Received target update:" << targetData.id;

    // Find existing target
    Target* target = m_mapWidget->getTarget(targetData.id);
    if (target) {
        // Update existing target
        target->updateData(targetData);

        // Update dialog if open
        auto dialogIt = m_targetDialogs.find(targetData.id);
        if (dialogIt != m_targetDialogs.end()) {
            dialogIt.value()->updateTargetData(targetData);
        }
    } else {
        // Create new target if not found
        onTargetDataReceived(targetData);
    }
}

void MainWindow::onTargetRemovalRequested(const QString& targetId)
{
    qDebug() << "Removing target:" << targetId;

    // Remove from map
    m_mapWidget->removeTarget(targetId);

    // Close dialog if open
    auto dialogIt = m_targetDialogs.find(targetId);
    if (dialogIt != m_targetDialogs.end()) {
        dialogIt.value()->close();
        dialogIt.value()->deleteLater();
        m_targetDialogs.erase(dialogIt);
    }

    // Update status
    updateStatusBar();
}

void MainWindow::onTargetClicked(Target* target)
{
    if (!target) {
        return;
    }

    qDebug() << "Target clicked:" << target->getId();

    // Send UDP response
    QJsonObject responseData;
    responseData["click_position"] = QString("%1,%2")
                                   .arg(target->getData().latitude, 0, 'f', 6)
                                   .arg(target->getData().longitude, 0, 'f', 6);
    responseData["click_time"] = QDateTime::currentDateTime().toString(Qt::ISODate);

    m_udpManager->sendTargetClickResponse(target->getId(), responseData);

    // Show target detail dialog
    showTargetDetailDialog(target);
}

void MainWindow::onMouseCoordinatesChanged(const QPointF& coordinates)
{
    if (!coordinates.isNull()) {
        m_coordinateStatusLabel->setText(
            QString("Lat: %1°, Lon: %2°")
            .arg(coordinates.y(), 0, 'f', 6)
            .arg(coordinates.x(), 0, 'f', 6)
        );
    } else {
        m_coordinateStatusLabel->setText("Coordinates: --");
    }
}

void MainWindow::onUdpError(const QString& error)
{
    qWarning() << "UDP Error:" << error;
    statusBar()->showMessage(QString("UDP Error: %1").arg(error), 5000);
}

void MainWindow::onConnectionStatusChanged(bool connected)
{
    if (connected) {
        m_connectionStatusLabel->setText(QString("UDP: Connected (:%1)").arg(UDP_LISTEN_PORT));
        m_connectionStatusLabel->setStyleSheet("color: green;");
    } else {
        m_connectionStatusLabel->setText("UDP: Disconnected");
        m_connectionStatusLabel->setStyleSheet("color: red;");
    }
}

void MainWindow::updateStatusBar()
{
    // Update target count
    int targetCount = m_mapWidget ? m_mapWidget->getTargetCount() : 0;
    m_targetCountLabel->setText(QString("Targets: %1").arg(targetCount));

    // Update zoom level
    if (m_mapWidget) {
        m_zoomStatusLabel->setText(QString("Zoom: %1").arg(m_mapWidget->zoomLevel()));
    }
}

void MainWindow::showTargetDetailDialog(Target* target)
{
    QString targetId = target->getId();

    // Check if dialog already exists
    auto it = m_targetDialogs.find(targetId);
    if (it != m_targetDialogs.end()) {
        // Bring existing dialog to front
        it.value()->raise();
        it.value()->activateWindow();
        return;
    }

    // Create new dialog
    TargetDetailDialog* dialog = new TargetDetailDialog(target->getData(), this);
    m_targetDialogs.insert(targetId, dialog);

    // Connect dialog signals
    connect(dialog, &TargetDetailDialog::trackTargetRequested, [this](const QString& id) {
        // Implement target tracking functionality
        Target* target = m_mapWidget->getTarget(id);
        if (target) {
            // Center map on target
            m_mapWidget->setCenter(target->position(), true);
            target->setState(Target::Highlighted);
        }
    });

    connect(dialog, &TargetDetailDialog::sendResponseRequested,
            [this](const QString& id, const QJsonObject& data) {
        m_udpManager->sendTargetClickResponse(id, data);
    });

    // Clean up when dialog is closed
    connect(dialog, &QDialog::finished, [this, targetId]() {
        m_targetDialogs.remove(targetId);
    });

    // Show dialog
    dialog->show();
}

void MainWindow::updateWindowTitle()
{
    QString title = "Professional Map Application";
    if (m_udpManager && m_udpManager->isListening()) {
        title += QString(" - Listening on port %1").arg(m_udpManager->getListeningPort());
    }
    setWindowTitle(title);
}

