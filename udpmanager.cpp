#include "udpmanager.h"
#include <QJsonParseError>
#include <QDebug>
#include <QDateTime>
#include <QNetworkDatagram>

UdpManager::UdpManager(QObject *parent)
    : QObject(parent)
    , m_udpSocket(new QUdpSocket(this))
    , m_listeningPort(0)
    , m_isListening(false)
    , m_connectionTimer(new QTimer(this))
    , m_connectionTimeout(DEFAULT_CONNECTION_TIMEOUT)
    , m_messagesReceived(0)
    , m_messagesSent(0)
    , m_parseErrors(0)
{
    // Setup connection monitoring
    m_connectionTimer->setInterval(5000); // Check every 5 seconds
    connect(m_connectionTimer, &QTimer::timeout, this, &UdpManager::checkConnectionHealth);
    
    // Connect socket signals
    connect(m_udpSocket, &QUdpSocket::readyRead, this, &UdpManager::onDatagramReceived);
    connect(m_udpSocket, QOverload<QAbstractSocket::SocketError>::of(&QUdpSocket::error),
            this, &UdpManager::onSocketError);
}

UdpManager::~UdpManager()
{
    stopListening();
}

bool UdpManager::startListening(quint16 port, const QHostAddress& address)
{
    if (m_isListening) {
        stopListening();
    }
    
    if (m_udpSocket->bind(address, port)) {
        m_listeningPort = port;
        m_isListening = true;
        m_lastMessageTime = QDateTime::currentDateTime();
        
        // Start connection monitoring
        m_connectionTimer->start();
        
        qDebug() << "UDP Manager started listening on port" << port;
        emit connectionStatusChanged(true);
        return true;
    } else {
        QString error = QString("Failed to bind to port %1: %2")
                       .arg(port).arg(m_udpSocket->errorString());
        qWarning() << error;
        emit udpError(error);
        return false;
    }
}

void UdpManager::stopListening()
{
    if (m_isListening) {
        m_udpSocket->close();
        m_connectionTimer->stop();
        m_isListening = false;
        m_listeningPort = 0;
        
        qDebug() << "UDP Manager stopped listening";
        emit connectionStatusChanged(false);
    }
}

bool UdpManager::sendTargetClickResponse(const QString& targetId, 
                                       const QJsonObject& responseData,
                                       const QHostAddress& destinationAddress,
                                       quint16 destinationPort)
{
    QJsonObject message = createResponseMessage(targetId, responseData);
    return sendMessage(message, destinationAddress, destinationPort);
}

bool UdpManager::sendMessage(const QJsonObject& message,
                           const QHostAddress& destinationAddress,
                           quint16 destinationPort)
{
    QJsonDocument doc(message);
    QByteArray data = doc.toJson(QJsonDocument::Compact);
    
    qint64 bytesWritten = m_udpSocket->writeDatagram(data, destinationAddress, destinationPort);
    
    if (bytesWritten == data.size()) {
        m_messagesSent++;
        qDebug() << "Sent UDP message to" << destinationAddress.toString() << ":" << destinationPort;
        return true;
    } else {
        QString error = QString("Failed to send UDP message: %1").arg(m_udpSocket->errorString());
        qWarning() << error;
        emit udpError(error);
        return false;
    }
}

bool UdpManager::isListening() const
{
    return m_isListening;
}

quint16 UdpManager::getListeningPort() const
{
    return m_listeningPort;
}

void UdpManager::setConnectionTimeout(int timeoutMs)
{
    m_connectionTimeout = timeoutMs;
}

void UdpManager::onDatagramReceived()
{
    while (m_udpSocket->hasPendingDatagrams()) {
        QNetworkDatagram datagram = m_udpSocket->receiveDatagram();
        QByteArray data = datagram.data();
        
        m_messagesReceived++;
        m_lastMessageTime = QDateTime::currentDateTime();
        
        // Parse JSON
        QJsonParseError parseError;
        QJsonDocument doc = QJsonDocument::fromJson(data, &parseError);
        
        if (parseError.error != QJsonParseError::NoError) {
            m_parseErrors++;
            QString error = QString("JSON parse error: %1").arg(parseError.errorString());
            qWarning() << error;
            emit udpError(error);
            continue;
        }
        
        if (!doc.isObject()) {
            m_parseErrors++;
            emit udpError("Received JSON is not an object");
            continue;
        }
        
        QJsonObject json = doc.object();
        
        // Determine message type and handle accordingly
        QString messageType = json.value("type").toString();
        
        if (messageType == "target" || messageType.isEmpty()) {
            handleTargetMessage(json);
        } else if (messageType == "control") {
            handleControlMessage(json);
        } else {
            qWarning() << "Unknown message type:" << messageType;
        }
    }
}

void UdpManager::onSocketError(QAbstractSocket::SocketError error)
{
    QString errorString = QString("UDP Socket Error (%1): %2")
                         .arg(error).arg(m_udpSocket->errorString());
    qWarning() << errorString;
    emit udpError(errorString);
    
    // Try to recover from certain errors
    if (error == QAbstractSocket::NetworkError || 
        error == QAbstractSocket::SocketResourceError) {
        // Attempt to restart listening
        if (m_isListening) {
            quint16 port = m_listeningPort;
            stopListening();
            QTimer::singleShot(1000, [this, port]() {
                startListening(port);
            });
        }
    }
}

void UdpManager::checkConnectionHealth()
{
    if (!m_isListening) {
        return;
    }
    
    // Check if we've received messages recently
    qint64 timeSinceLastMessage = m_lastMessageTime.msecsTo(QDateTime::currentDateTime());
    
    if (timeSinceLastMessage > m_connectionTimeout) {
        qDebug() << "No UDP messages received for" << timeSinceLastMessage << "ms";
        // This is just informational - UDP is connectionless
    }
    
    // Log statistics periodically
    static int logCounter = 0;
    if (++logCounter >= 12) { // Every minute (5s * 12)
        qDebug() << "UDP Stats - Received:" << m_messagesReceived 
                 << "Sent:" << m_messagesSent 
                 << "Parse Errors:" << m_parseErrors;
        logCounter = 0;
    }
}

bool UdpManager::parseTargetMessage(const QJsonObject& json, TargetData& targetData)
{
    // Required fields
    targetData.id = json.value("id").toString();
    targetData.targetType = json.value("targettype").toString();
    
    // Coordinates
    targetData.latitude = json.value("latitude").toDouble();
    targetData.longitude = json.value("longitude").toDouble();
    targetData.altitude = json.value("altitude").toDouble(0.0);
    
    // Optional fields
    targetData.name = json.value("name").toString();
    targetData.description = json.value("description").toString();
    
    // Store additional data
    targetData.additionalData = json;
    
    return targetData.isValid();
}

void UdpManager::handleTargetMessage(const QJsonObject& json)
{
    TargetData targetData;
    
    if (!parseTargetMessage(json, targetData)) {
        m_parseErrors++;
        emit udpError("Invalid target data received");
        return;
    }
    
    // Check for action type
    QString action = json.value("action").toString("add");
    
    if (action == "add" || action == "update") {
        if (action == "add") {
            emit targetDataReceived(targetData);
        } else {
            emit targetUpdateReceived(targetData);
        }
    } else if (action == "remove") {
        emit targetRemovalRequested(targetData.id);
    } else {
        qWarning() << "Unknown target action:" << action;
    }
}

void UdpManager::handleControlMessage(const QJsonObject& json)
{
    QString command = json.value("command").toString();
    
    if (command == "ping") {
        // Respond to ping
        QJsonObject response;
        response["type"] = "control";
        response["command"] = "pong";
        response["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);
        
        sendMessage(response);
    } else if (command == "status") {
        // Send status information
        QJsonObject response;
        response["type"] = "control";
        response["command"] = "status_response";
        response["listening"] = m_isListening;
        response["port"] = static_cast<int>(m_listeningPort);
        response["messages_received"] = static_cast<int>(m_messagesReceived);
        response["messages_sent"] = static_cast<int>(m_messagesSent);
        response["parse_errors"] = static_cast<int>(m_parseErrors);
        
        sendMessage(response);
    } else {
        qWarning() << "Unknown control command:" << command;
    }
}

QJsonObject UdpManager::createResponseMessage(const QString& targetId, const QJsonObject& additionalData)
{
    QJsonObject message;
    message["type"] = "target_click_response";
    message["target_id"] = targetId;
    message["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    message["client_id"] = "qt_map_application";
    
    // Add any additional data
    for (auto it = additionalData.begin(); it != additionalData.end(); ++it) {
        message[it.key()] = it.value();
    }
    
    return message;
}
