#include "target.h"
#include <QApplication>
#include <QDir>
#include <QStandardPaths>
#include <QDebug>
#include <QEasingCurve>
#include <cmath>

// Static icon cache
QHash<QString, QPixmap> Target::s_iconCache;

Target::Target(const UdpManager::TargetData& data, QObject *parent)
    : QObject(parent)
    , m_data(data)
    , m_lastUpdateTime(QDateTime::currentDateTime())
    , m_iconSize(DEFAULT_ICON_SIZE, DEFAULT_ICON_SIZE)
    , m_state(Normal)
    , m_opacity(NORMAL_OPACITY)
    , m_visible(true)
    , m_positionAnimation(nullptr)
    , m_opacityAnimation(nullptr)
    , m_blinkAnimation(nullptr)
{
    setupAnimations();
    loadIcon();
}

Target::~Target()
{
    if (m_positionAnimation) {
        m_positionAnimation->stop();
    }
    if (m_opacityAnimation) {
        m_opacityAnimation->stop();
    }
    if (m_blinkAnimation) {
        m_blinkAnimation->stop();
    }
}

void Target::updateData(const UdpManager::TargetData& data)
{
    bool positionChanged = (m_data.latitude != data.latitude || m_data.longitude != data.longitude);
    bool typeChanged = (m_data.targetType != data.targetType);
    
    m_data = data;
    m_lastUpdateTime = QDateTime::currentDateTime();
    
    // Reload icon if type changed
    if (typeChanged) {
        loadIcon();
    }
    
    // Animate position change if needed
    if (positionChanged) {
        emit positionChanged();
    }
    
    emit needsRepaint();
}

void Target::setPosition(const QPointF& position, bool animated)
{
    if (position == this->position()) {
        return;
    }
    
    m_data.longitude = position.x();
    m_data.latitude = position.y();
    
    if (animated && m_positionAnimation) {
        // Start position animation
        m_positionAnimation->setDuration(ANIMATION_DURATION);
        m_positionAnimation->setEasingCurve(QEasingCurve::OutCubic);
        m_positionAnimation->start();
    }
    
    emit positionChanged();
    emit needsRepaint();
}

void Target::setScreenPosition(const QPointF& screenPos)
{
    if (m_screenPosition != screenPos) {
        m_screenPosition = screenPos;
        emit needsRepaint();
    }
}

void Target::setIconSize(const QSize& size)
{
    if (m_iconSize != size) {
        m_iconSize = size;
        loadIcon(); // Reload with new size
        emit needsRepaint();
    }
}

QRect Target::getBoundingRect() const
{
    QPoint topLeft = m_screenPosition.toPoint() - QPoint(m_iconSize.width() / 2, m_iconSize.height() / 2);
    return QRect(topLeft, m_iconSize);
}

bool Target::contains(const QPointF& point) const
{
    if (!m_visible || m_opacity <= 0.0) {
        return false;
    }
    
    return getBoundingRect().contains(point.toPoint());
}

void Target::setState(TargetState state)
{
    if (m_state != state) {
        m_state = state;
        
        // Update opacity based on state
        qreal targetOpacity = NORMAL_OPACITY;
        switch (state) {
            case Normal:
                targetOpacity = NORMAL_OPACITY;
                break;
            case Hovered:
                targetOpacity = HOVER_OPACITY;
                break;
            case Selected:
                targetOpacity = SELECTED_OPACITY;
                break;
            case Highlighted:
                targetOpacity = SELECTED_OPACITY;
                startBlinking();
                break;
        }
        
        if (state != Highlighted) {
            stopBlinking();
        }
        
        // Animate opacity change
        if (m_opacityAnimation) {
            m_opacityAnimation->setStartValue(m_opacity);
            m_opacityAnimation->setEndValue(targetOpacity);
            m_opacityAnimation->setDuration(200);
            m_opacityAnimation->start();
        } else {
            setOpacity(targetOpacity);
        }
        
        emit stateChanged(state);
        emit needsRepaint();
    }
}

void Target::setOpacity(qreal opacity)
{
    opacity = qBound(0.0, opacity, 1.0);
    if (m_opacity != opacity) {
        m_opacity = opacity;
        emit opacityChanged();
        emit needsRepaint();
    }
}

void Target::setVisible(bool visible)
{
    if (m_visible != visible) {
        m_visible = visible;
        emit needsRepaint();
    }
}

void Target::startBlinking()
{
    if (m_blinkAnimation && m_blinkAnimation->state() != QAbstractAnimation::Running) {
        m_blinkAnimation->setStartValue(m_opacity);
        m_blinkAnimation->setEndValue(0.3);
        m_blinkAnimation->setDuration(500);
        m_blinkAnimation->setLoopCount(-1); // Infinite loop
        m_blinkAnimation->start();
    }
}

void Target::stopBlinking()
{
    if (m_blinkAnimation && m_blinkAnimation->state() == QAbstractAnimation::Running) {
        m_blinkAnimation->stop();
        setOpacity(NORMAL_OPACITY);
    }
}

void Target::onPositionAnimationFinished()
{
    emit needsRepaint();
}

void Target::onBlinkAnimationFinished()
{
    // This is called when blinking animation completes one cycle
    // For infinite loops, this won't be called unless manually stopped
}

void Target::loadIcon()
{
    QString iconPath = getIconPath();
    
    // Check cache first
    if (s_iconCache.contains(iconPath)) {
        m_icon = s_iconCache[iconPath];
        if (!m_icon.isNull()) {
            m_icon = m_icon.scaled(m_iconSize, Qt::KeepAspectRatio, Qt::SmoothTransformation);
            return;
        }
    }
    
    // Load from file
    QPixmap originalIcon(iconPath);
    if (originalIcon.isNull()) {
        // Create a default icon if file not found
        originalIcon = QPixmap(32, 32);
        originalIcon.fill(Qt::red);
        qWarning() << "Could not load icon:" << iconPath << "- using default";
    }
    
    // Cache the original
    s_iconCache[iconPath] = originalIcon;
    
    // Scale for current use
    m_icon = originalIcon.scaled(m_iconSize, Qt::KeepAspectRatio, Qt::SmoothTransformation);
}

QString Target::getIconPath() const
{
    // Determine icon based on target type
    QString iconName;
    
    // Simple logic: use red.png for certain types, blue.png for others
    // This can be expanded based on specific requirements
    QStringList redTypes = {"enemy", "threat", "hostile", "danger", "alert"};
    QStringList blueTypes = {"friendly", "ally", "safe", "neutral", "info"};
    
    QString lowerType = m_data.targetType.toLower();
    
    if (redTypes.contains(lowerType) || lowerType.contains("red")) {
        iconName = "red.png";
    } else if (blueTypes.contains(lowerType) || lowerType.contains("blue")) {
        iconName = "blue.png";
    } else {
        // Default based on some other criteria or use blue as default
        iconName = "blue.png";
    }
    
    // Look for icon in application directory first, then resources
    QString appDir = QApplication::applicationDirPath();
    QString iconPath = appDir + "/icons/" + iconName;
    
    if (QFile::exists(iconPath)) {
        return iconPath;
    }
    
    // Try resources
    iconPath = ":/icons/" + iconName;
    if (QFile::exists(iconPath)) {
        return iconPath;
    }
    
    // Try current directory
    iconPath = "./" + iconName;
    if (QFile::exists(iconPath)) {
        return iconPath;
    }
    
    qWarning() << "Icon not found:" << iconName;
    return QString(); // Will trigger default icon creation
}

void Target::setupAnimations()
{
    // Position animation
    m_positionAnimation = new QPropertyAnimation(this, "position", this);
    connect(m_positionAnimation, &QPropertyAnimation::finished,
            this, &Target::onPositionAnimationFinished);
    
    // Opacity animation
    m_opacityAnimation = new QPropertyAnimation(this, "opacity", this);
    
    // Blink animation
    m_blinkAnimation = new QPropertyAnimation(this, "opacity", this);
    m_blinkAnimation->setDirection(QAbstractAnimation::Forward);
    connect(m_blinkAnimation, &QPropertyAnimation::finished,
            this, &Target::onBlinkAnimationFinished);
}
