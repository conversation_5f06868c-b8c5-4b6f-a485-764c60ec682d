#ifndef MAPWIDGET_H
#define MAPWIDGET_H

#include <QWidget>
#include <QPixmap>
#include <QPoint>
#include <QPointF>
#include <QRect>
#include <QTimer>
#include <QLabel>
#include <QHash>
#include <QMutex>
#include <QPropertyAnimation>
#include <QGestureEvent>
#include "tilemanager.h"
#include "coordinateconverter.h"
#include "target.h"
#include "udpmanager.h"

/**
 * @brief Main map widget with tile rendering and interaction
 * 
 * Features:
 * - Smooth drag panning and mouse wheel zooming
 * - TMS tile rendering with caching
 * - Target display and interaction
 * - Coordinate display
 * - Viewport-aware tile loading
 * - Performance optimizations
 */
class MapWidget : public QWidget
{
    Q_OBJECT
    Q_PROPERTY(QPointF center READ center WRITE setCenter NOTIFY centerChanged)
    Q_PROPERTY(int zoomLevel READ zoomLevel WRITE setZoomLevel NOTIFY zoomLevelChanged)

public:
    explicit MapWidget(QWidget *parent = nullptr);
    ~MapWidget();
    
    /**
     * @brief Get current map center (lat/lon)
     */
    QPointF center() const { return m_center; }
    
    /**
     * @brief Set map center (lat/lon)
     * @param center New center coordinates
     * @param animated Whether to animate the transition
     */
    void setCenter(const QPointF& center, bool animated = false);
    
    /**
     * @brief Get current zoom level
     */
    int zoomLevel() const { return m_zoomLevel; }
    
    /**
     * @brief Set zoom level
     * @param zoom New zoom level (0-18)
     * @param animated Whether to animate the transition
     */
    void setZoomLevel(int zoom, bool animated = false);
    
    /**
     * @brief Zoom to specific bounds
     * @param bounds Rectangle in lat/lon coordinates
     */
    void zoomToBounds(const QRectF& bounds);
    
    /**
     * @brief Add target to map
     * @param target Target to add
     */
    void addTarget(Target* target);
    
    /**
     * @brief Remove target from map
     * @param targetId ID of target to remove
     */
    void removeTarget(const QString& targetId);
    
    /**
     * @brief Get target by ID
     * @param targetId Target ID
     * @return Target pointer or nullptr if not found
     */
    Target* getTarget(const QString& targetId);
    
    /**
     * @brief Clear all targets
     */
    void clearTargets();

    /**
     * @brief Get number of targets
     */
    int getTargetCount() const { return m_targets.size(); }

    /**
     * @brief Convert screen coordinates to lat/lon
     * @param screenPos Screen position in pixels
     * @return Geographic coordinates
     */
    QPointF screenToLatLon(const QPointF& screenPos) const;
    
    /**
     * @brief Convert lat/lon to screen coordinates
     * @param latLon Geographic coordinates
     * @return Screen position in pixels
     */
    QPointF latLonToScreen(const QPointF& latLon) const;
    
    /**
     * @brief Get current mouse cursor coordinates
     */
    QPointF getMouseCoordinates() const { return m_mouseCoordinates; }

signals:
    /**
     * @brief Emitted when map center changes
     */
    void centerChanged();
    
    /**
     * @brief Emitted when zoom level changes
     */
    void zoomLevelChanged();
    
    /**
     * @brief Emitted when target is clicked
     * @param target Clicked target
     */
    void targetClicked(Target* target);
    
    /**
     * @brief Emitted when mouse coordinates change
     * @param coordinates Current mouse lat/lon coordinates
     */
    void mouseCoordinatesChanged(const QPointF& coordinates);

protected:
    void paintEvent(QPaintEvent* event) override;
    void mousePressEvent(QMouseEvent* event) override;
    void mouseMoveEvent(QMouseEvent* event) override;
    void mouseReleaseEvent(QMouseEvent* event) override;
    void wheelEvent(QWheelEvent* event) override;
    void resizeEvent(QResizeEvent* event) override;
    void leaveEvent(QEvent* event) override;

private slots:
    void onTileLoaded(int zoom, int x, int y, const QPixmap& pixmap);
    void onTileLoadFailed(int zoom, int x, int y, const QString& error);
    void onTargetNeedsRepaint();
    void onCenterAnimationFinished();
    void onZoomAnimationFinished();
    void updateTargetPositions();
    void scheduleRepaint();

private:
    void setupUI();
    void requestVisibleTiles();
    void drawTiles(QPainter& painter);
    void drawTargets(QPainter& painter);
    void drawCoordinateDisplay(QPainter& painter);
    void drawZoomLevel(QPainter& painter);
    
    QRect getVisibleTileRect() const;
    QPointF getTileOffset() const;
    Target* getTargetAtPosition(const QPointF& pos) const;
    void updateMouseCoordinates(const QPointF& screenPos);
    void startCenterAnimation(const QPointF& targetCenter);
    void startZoomAnimation(int targetZoom);
    
    // Map state
    QPointF m_center;           // Current center (lat/lon)
    int m_zoomLevel;            // Current zoom level
    QPointF m_mouseCoordinates; // Current mouse coordinates (lat/lon)
    
    // Interaction state
    bool m_dragging;
    QPoint m_lastPanPoint;
    QPoint m_dragStartPoint;
    
    // Components
    TileManager* m_tileManager;
    QHash<QString, Target*> m_targets;
    
    // UI elements
    QLabel* m_coordinateLabel;
    QLabel* m_zoomLabel;
    
    // Animations
    QPropertyAnimation* m_centerAnimation;
    QPropertyAnimation* m_zoomAnimation;
    
    // Performance optimization
    QTimer* m_repaintTimer;
    bool m_needsRepaint;
    
    // Constants
    static constexpr int MIN_ZOOM_LEVEL = 0;
    static constexpr int MAX_ZOOM_LEVEL = 18;
    static constexpr int TILE_SIZE = 256;
    static constexpr int ANIMATION_DURATION = 300; // ms
    static constexpr int REPAINT_INTERVAL = 16; // ~60 FPS
};

#endif // MAPWIDGET_H
